# 🤖 Vanna + DeepSeek 配置指南

## 📋 前提条件

1. **DeepSeek API 密钥**
   - 访问 [DeepSeek 平台](https://platform.deepseek.com/)
   - 注册账号并获取 API 密钥
   - DeepSeek 提供免费额度，非常适合开始使用

2. **已安装的依赖**
   - ✅ `vanna` (已安装)
   - ✅ `openai` (已安装)
   - ✅ `chromadb` (Vanna 自带)

## 🔧 配置步骤

### 1. 基本配置

```python
from vanna.openai import OpenAI_Chat
from vanna.chromadb import ChromaDB_VectorStore
from openai import OpenAI

# 创建 DeepSeek 客户端
deepseek_client = OpenAI(
    api_key="your-deepseek-api-key",
    base_url="https://api.deepseek.com"
)

# 创建自定义 Vanna 类
class VannaDeepSeek(ChromaDB_VectorStore, OpenAI_Chat):
    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, client=deepseek_client, config=config)

# 初始化
vn = VannaDeepSeek(config={'model': 'deepseek-chat'})
```

### 2. 可用的 DeepSeek 模型

- `deepseek-chat` - 通用对话模型
- `deepseek-coder` - 专门用于代码生成（推荐用于 SQL）

### 3. 完整示例

运行以下命令开始使用：

```bash
python vanna_deepseek.py
```

## 🎯 使用方式

### 命令行模式
```bash
python vanna_deepseek.py
# 选择选项 1 或 3
```

### Web 界面模式
```bash
python vanna_deepseek.py
# 选择选项 2
```

## 💰 成本优势

使用 DeepSeek 的优势：
- **成本低** - 比 OpenAI 便宜很多
- **性能好** - DeepSeek-V3 在代码生成方面表现优秀
- **中文支持** - 对中文查询支持更好
- **免费额度** - 新用户有免费使用额度

## 🔄 与其他 LLM 的对比

| LLM | 成本 | 中文支持 | SQL 生成质量 | 设置难度 |
|-----|------|----------|--------------|----------|
| DeepSeek | 很低 | 优秀 | 很好 | 简单 |
| OpenAI GPT-4 | 高 | 好 | 优秀 | 简单 |
| Ollama (本地) | 免费 | 一般 | 好 | 中等 |

## 🚀 快速开始

1. **获取 DeepSeek API 密钥**
   - 访问 https://platform.deepseek.com/
   - 注册并获取 API 密钥

2. **运行演示**
   ```bash
   python vanna_deepseek.py
   ```

3. **输入 API 密钥**
   - 按提示输入你的 DeepSeek API 密钥

4. **开始查询**
   - 用中文或英文提问
   - 查看生成的 SQL 和结果

## 💡 使用技巧

1. **中文查询** - DeepSeek 对中文支持很好，可以直接用中文提问
2. **模型选择** - 对于 SQL 生成，推荐使用 `deepseek-coder`
3. **训练数据** - 添加中文的业务文档可以提高查询准确性

## 🔧 高级配置

如果你想自定义更多设置：

```python
# 使用 deepseek-coder 模型
vn = VannaDeepSeek(config={
    'model': 'deepseek-coder',
    'temperature': 0.1,  # 降低随机性，提高一致性
    'max_tokens': 1000
})
```

## 🐛 常见问题

### Q: API 密钥无效
**A**: 检查密钥是否正确，确保在 DeepSeek 平台上已激活

### Q: 连接超时
**A**: 检查网络连接，DeepSeek API 需要访问国外服务器

### Q: 中文查询效果不好
**A**: 尝试使用 `deepseek-coder` 模型，并添加更多中文训练数据

## 📞 需要帮助？

如果遇到问题，请提供：
- DeepSeek API 密钥状态
- 具体的错误信息
- 使用的模型名称
