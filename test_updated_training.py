"""
测试更新后的训练数据
==================

快速测试新的项目管理数据库训练数据效果
"""

def test_training_data():
    """测试训练数据"""
    
    print("🧪 测试项目管理训练数据")
    print("=" * 40)
    
    # 导入训练数据
    try:
        from project_training_data import train_project_database, train_business_documentation
        print("✅ 训练数据模块导入成功")
        
        # 显示训练数据统计
        import inspect
        source = inspect.getsource(train_project_database)
        
        # 统计训练示例数量
        question_count = source.count('"question":')
        print(f"📊 包含 {question_count} 个查询示例")
        
        # 显示训练数据类别
        categories = [
            "基础统计查询",
            "时间范围查询", 
            "审计相关查询",
            "状态相关查询",
            "费用相关查询",
            "设计单位相关查询",
            "施工单位相关查询",
            "复合查询",
            "按年度统计",
            "用户和采购相关"
        ]
        
        print("\n📋 训练数据类别:")
        for i, category in enumerate(categories, 1):
            print(f"   {i:2d}. {category}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def show_sample_queries():
    """显示示例查询"""
    
    print("\n💡 基于你的数据库的示例查询:")
    print("=" * 40)
    
    sample_queries = [
        "库中一共多少工程",
        "2020年以后的工程有多少项", 
        "已经审计的工程占比",
        "需要审计的工程数量",
        "已验收的工程数量",
        "总费用超过10万的工程",
        "工程总费用统计",
        "有多少个设计单位",
        "各施工单位的工程数量",
        "2021年费用超过5万的工程",
        "按年度统计工程数量",
        "已验收但未审计的工程",
        "费用最高的10个工程",
        "本月提交的工程",
        "平均工程费用"
    ]
    
    for i, query in enumerate(sample_queries, 1):
        print(f"   {i:2d}. {query}")
    
    print(f"\n✨ 总共 {len(sample_queries)} 个示例查询")

def compare_with_original():
    """与原始训练数据对比"""
    
    print("\n🔄 训练数据改进对比:")
    print("=" * 30)
    
    print("📈 改进前 (通用 MySQL 训练):")
    print("   - 通用的 MySQL 语法说明")
    print("   - 基础的数据库操作示例")
    print("   - 没有业务上下文")
    
    print("\n📈 改进后 (项目管理专用训练):")
    print("   - 50+ 个具体的业务查询示例")
    print("   - 详细的字段含义说明")
    print("   - 业务流程和规则说明")
    print("   - 基于实际使用场景的查询模式")
    
    print("\n🎯 预期效果提升:")
    print("   ✅ 更准确的字段识别")
    print("   ✅ 更符合业务逻辑的 SQL")
    print("   ✅ 更好的中文查询理解")
    print("   ✅ 更高的查询成功率")

def main():
    """主函数"""
    print("🔧 训练数据更新测试")
    print("=" * 40)
    
    # 测试训练数据
    if test_training_data():
        show_sample_queries()
        compare_with_original()
        
        print("\n🚀 使用方法:")
        print("1. 运行: python vanna_deepseek_mysql.py")
        print("2. 选择自动训练 (y)")
        print("3. 系统会自动加载专用训练数据")
        print("4. 开始用中文查询你的项目数据库！")
        
        print("\n💡 测试建议:")
        print("- 尝试上面列出的示例查询")
        print("- 对比查询结果的准确性")
        print("- 测试复杂的业务查询")
    else:
        print("❌ 训练数据测试失败")

if __name__ == "__main__":
    main()
