"""
测试 Vanna 安装
==============

验证 Vanna 是否正确安装并可以使用
"""

def test_vanna_import():
    """测试 Vanna 导入"""
    try:
        import vanna
        print("✅ Vanna 核心模块导入成功")
        print(f"   版本: {vanna.__version__ if hasattr(vanna, '__version__') else '未知'}")
        return True
    except ImportError as e:
        print(f"❌ Vanna 导入失败: {e}")
        return False

def test_remote_import():
    """测试远程服务导入"""
    try:
        from vanna.remote import VannaDefault
        print("✅ Vanna 远程服务模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ Vanna 远程服务导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        import vanna
        
        # 测试 API 密钥获取功能（不实际调用）
        print("✅ 基本功能测试通过")
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_optional_dependencies():
    """测试可选依赖"""
    optional_deps = {
        'pandas': 'pandas',
        'plotly': 'plotly',
        'flask': 'flask',
        'sqlalchemy': 'sqlalchemy'
    }
    
    for name, module in optional_deps.items():
        try:
            __import__(module)
            print(f"✅ {name} 可用")
        except ImportError:
            print(f"⚠️  {name} 不可用（可选）")

def main():
    """主测试函数"""
    print("🧪 Vanna 安装测试")
    print("=" * 30)
    
    tests = [
        ("核心模块导入", test_vanna_import),
        ("远程服务导入", test_remote_import),
        ("基本功能", test_basic_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        if test_func():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Vanna 安装成功")
        print("\n🚀 下一步:")
        print("1. 运行 'python demo.py' 开始演示")
        print("2. 查看 README.md 了解更多信息")
    else:
        print("❌ 部分测试失败，请检查安装")
    
    print("\n📦 可选依赖检查:")
    test_optional_dependencies()

if __name__ == "__main__":
    main()
