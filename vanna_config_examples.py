"""
Vanna AI 配置示例
================

展示不同的 LLM 和向量数据库组合配置
"""

# ============================================================================
# 配置 1: OpenAI + ChromaDB (推荐用于开始)
# ============================================================================

def setup_openai_chromadb():
    """OpenAI + ChromaDB 配置"""
    from vanna.openai.openai_chat import OpenAI_Chat
    from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
    
    class VannaOpenAI(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            OpenAI_Chat.__init__(self, config=config)
    
    # 配置
    config = {
        'api_key': 'sk-your-openai-api-key',  # 替换为你的密钥
        'model': 'gpt-4',  # 或 'gpt-3.5-turbo'
    }
    
    return VannaOpenAI(config=config)


# ============================================================================
# 配置 2: Ollama + ChromaDB (完全本地化)
# ============================================================================

def setup_ollama_chromadb():
    """Ollama + ChromaDB 配置（完全本地）"""
    from vanna.ollama import Ollama
    from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
    
    class VannaOllama(ChromaDB_VectorStore, Ollama):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            Ollama.__init__(self, config=config)
    
    # 配置
    config = {
        'model': 'llama2',  # 或其他已安装的模型
        'ollama_host': 'http://localhost:11434'
    }
    
    return VannaOllama(config=config)


# ============================================================================
# 配置 3: Azure OpenAI + Qdrant
# ============================================================================

def setup_azure_openai_qdrant():
    """Azure OpenAI + Qdrant 配置"""
    from vanna.azure_openai.azure_openai_chat import AzureOpenAI_Chat
    from vanna.qdrant.qdrant_vector import Qdrant_VectorStore
    
    class VannaAzure(Qdrant_VectorStore, AzureOpenAI_Chat):
        def __init__(self, config=None):
            Qdrant_VectorStore.__init__(self, config=config)
            AzureOpenAI_Chat.__init__(self, config=config)
    
    # 配置
    config = {
        'azure_openai_endpoint': 'https://your-resource.openai.azure.com/',
        'azure_openai_api_key': 'your-azure-api-key',
        'azure_openai_api_version': '2023-05-15',
        'deployment_name': 'your-deployment-name',
        'qdrant_host': 'localhost',
        'qdrant_port': 6333
    }
    
    return VannaAzure(config=config)


# ============================================================================
# 数据库连接示例
# ============================================================================

def database_connections(vn):
    """不同数据库的连接方法"""
    
    # SQLite
    # vn.connect_to_sqlite('path/to/database.db')
    
    # PostgreSQL
    # vn.connect_to_postgres(
    #     host='localhost',
    #     dbname='your_database',
    #     user='your_username',
    #     password='your_password',
    #     port=5432
    # )
    
    # MySQL
    # vn.connect_to_mysql(
    #     host='localhost',
    #     dbname='your_database',
    #     user='your_username',
    #     password='your_password',
    #     port=3306
    # )
    
    # Snowflake
    # vn.connect_to_snowflake(
    #     account='your-account',
    #     username='your-username',
    #     password='your-password',
    #     database='your-database',
    #     schema='your-schema'
    # )
    
    # BigQuery
    # vn.connect_to_bigquery(
    #     cred_file_path='path/to/credentials.json',
    #     project_id='your-project-id',
    #     dataset_id='your-dataset-id'
    # )
    
    print("数据库连接方法示例（取消注释相应的代码）")


# ============================================================================
# 训练示例
# ============================================================================

def training_examples(vn):
    """训练模型的不同方法"""
    
    # 1. DDL 训练（数据库结构）
    vn.train(ddl="""
        CREATE TABLE employees (
            id INT PRIMARY KEY,
            name VARCHAR(100),
            department VARCHAR(50),
            salary DECIMAL(10,2),
            hire_date DATE
        );
    """)
    
    # 2. 文档训练（业务知识）
    vn.train(documentation="""
        员工表包含公司所有员工的信息。
        部门包括：IT、销售、市场、人力资源。
        薪资以美元为单位。
    """)
    
    # 3. SQL 示例训练
    vn.train(sql="SELECT department, AVG(salary) FROM employees GROUP BY department")
    
    # 4. 问题-SQL 对训练
    vn.train(
        question="每个部门的平均薪资是多少？",
        sql="SELECT department, AVG(salary) as avg_salary FROM employees GROUP BY department"
    )
    
    print("训练完成！模型现在了解你的数据库结构和业务逻辑。")


# ============================================================================
# 使用示例
# ============================================================================

def usage_examples():
    """使用 Vanna 的不同方法"""
    
    # 假设已经设置好 vn
    # vn = setup_openai_chromadb()
    
    print("=== 使用示例 ===")
    
    # 1. 生成 SQL
    # sql = vn.generate_sql("显示薪资最高的 5 名员工")
    # print(f"生成的 SQL: {sql}")
    
    # 2. 执行查询并获取结果
    # df = vn.run_sql(sql)
    # print(f"查询结果: {df}")
    
    # 3. 一步完成（生成 SQL + 执行 + 返回结果）
    # result = vn.ask("每个部门有多少员工？")
    # print(f"完整结果: {result}")
    
    # 4. 获取可视化图表
    # chart = vn.get_plotly_figure(
    #     question="显示每个部门的员工数量分布",
    #     sql="SELECT department, COUNT(*) as count FROM employees GROUP BY department"
    # )
    # chart.show()
    
    print("使用方法示例（取消注释相应代码来运行）")


if __name__ == "__main__":
    print("Vanna AI 配置示例")
    print("=" * 30)
    
    print("\n可用配置:")
    print("1. OpenAI + ChromaDB")
    print("2. Ollama + ChromaDB (本地)")
    print("3. Azure OpenAI + Qdrant")
    
    print("\n要开始使用，请:")
    print("1. 选择一个配置")
    print("2. 设置相应的 API 密钥")
    print("3. 连接到你的数据库")
    print("4. 训练模型")
    print("5. 开始提问！")
    
    usage_examples()
