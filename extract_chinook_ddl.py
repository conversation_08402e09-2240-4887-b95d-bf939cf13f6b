"""
从 Chinook 数据库提取 DDL
========================

演示如何从现有数据库提取 DDL 用于 Vanna 训练
"""

import sqlite3
import os

def extract_chinook_ddl():
    """从 Chinook.sqlite 提取 DDL"""
    
    if not os.path.exists('Chinook.sqlite'):
        print("❌ Chinook.sqlite 文件不存在")
        print("💡 请先运行其他脚本下载示例数据库")
        return None
    
    print("📊 从 Chinook 数据库提取 DDL...")
    
    try:
        conn = sqlite3.connect('Chinook.sqlite')
        cursor = conn.cursor()
        
        # 获取所有表的建表语句
        cursor.execute("""
            SELECT name, sql 
            FROM sqlite_master 
            WHERE type='table' 
            AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        """)
        
        tables = cursor.fetchall()
        
        if not tables:
            print("❌ 没有找到表")
            return None
        
        # 构建 DDL 内容
        ddl_content = "-- Chinook Database DDL\n"
        ddl_content += "-- Music Store Database Schema\n"
        ddl_content += f"-- Extracted on: {__import__('datetime').datetime.now()}\n\n"
        
        print(f"📋 找到 {len(tables)} 个表:")
        
        for table_name, table_sql in tables:
            print(f"   - {table_name}")
            
            if table_sql:  # 确保 SQL 不为空
                ddl_content += f"-- Table: {table_name}\n"
                ddl_content += table_sql + ";\n\n"
        
        # 保存到文件
        output_file = "chinook_ddl.sql"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(ddl_content)
        
        conn.close()
        
        print(f"✅ DDL 已保存到: {output_file}")
        return ddl_content
        
    except Exception as e:
        print(f"❌ 提取 DDL 失败: {e}")
        return None

def create_vanna_training_script(ddl_content):
    """创建 Vanna 训练脚本"""
    
    if not ddl_content:
        return
    
    training_script = '''"""
使用 Chinook DDL 训练 Vanna
==========================
"""

def train_vanna_with_chinook_ddl(vn):
    """使用 Chinook DDL 训练 Vanna"""
    
    print("🎓 使用 Chinook DDL 训练 Vanna...")
    
    # 完整的 DDL 内容
    chinook_ddl = """''' + ddl_content.replace('"""', '\\"\\"\\"') + '''"""
    
    # 方法 1: 一次性训练所有 DDL
    vn.train(ddl=chinook_ddl)
    
    # 方法 2: 分表训练（推荐）
    ddl_statements = []
    current_statement = ""
    
    for line in chinook_ddl.split('\\n'):
        if line.strip() and not line.startswith('--'):
            current_statement += line + "\\n"
            if line.strip().endswith(';'):
                ddl_statements.append(current_statement.strip())
                current_statement = ""
    
    print(f"📊 分别训练 {len(ddl_statements)} 个 DDL 语句...")
    
    for i, ddl in enumerate(ddl_statements, 1):
        if ddl.upper().startswith('CREATE TABLE'):
            table_name = ddl.split('(')[0].replace('CREATE TABLE', '').strip()
            print(f"   {i}. 训练表: {table_name}")
            vn.train(ddl=ddl)
    
    # 添加业务文档
    vn.train(documentation="""
        Chinook 是一个音乐商店数据库，包含以下主要实体：
        - Artist: 艺术家信息
        - Album: 专辑信息  
        - Track: 音轨/歌曲信息
        - Customer: 客户信息
        - Invoice: 发票/订单信息
        - InvoiceLine: 发票明细
        - Employee: 员工信息
        - Genre: 音乐类型
        - MediaType: 媒体类型
        - Playlist: 播放列表
        - PlaylistTrack: 播放列表和音轨的关联
    """)
    
    print("✅ Chinook DDL 训练完成！")

# 使用示例
if __name__ == "__main__":
    # 假设你已经初始化了 vn
    # train_vanna_with_chinook_ddl(vn)
    print("请在你的 Vanna 脚本中调用 train_vanna_with_chinook_ddl(vn)")
'''
    
    # 保存训练脚本
    with open('chinook_vanna_training.py', 'w', encoding='utf-8') as f:
        f.write(training_script)
    
    print("✅ Vanna 训练脚本已保存到: chinook_vanna_training.py")

def demonstrate_ddl_usage():
    """演示如何使用提取的 DDL"""
    
    print("\n🎯 DDL 使用方法:")
    print("=" * 30)
    
    print("1. 直接在 Vanna 中使用:")
    print("   ```python")
    print("   with open('chinook_ddl.sql', 'r') as f:")
    print("       ddl = f.read()")
    print("   vn.train(ddl=ddl)")
    print("   ```")
    
    print("\n2. 使用生成的训练脚本:")
    print("   ```python")
    print("   from chinook_vanna_training import train_vanna_with_chinook_ddl")
    print("   train_vanna_with_chinook_ddl(vn)")
    print("   ```")
    
    print("\n3. 分表训练（推荐）:")
    print("   - 每个表单独训练")
    print("   - 更好的控制和调试")
    print("   - 可以针对特定表添加额外说明")

def main():
    """主函数"""
    print("🗄️ Chinook DDL 提取工具")
    print("=" * 40)
    
    # 提取 DDL
    ddl_content = extract_chinook_ddl()
    
    if ddl_content:
        # 创建训练脚本
        create_vanna_training_script(ddl_content)
        
        # 显示使用方法
        demonstrate_ddl_usage()
        
        print("\n📁 生成的文件:")
        print("- chinook_ddl.sql: DDL 文件")
        print("- chinook_vanna_training.py: Vanna 训练脚本")
        
        # 显示 DDL 预览
        print("\n👀 DDL 预览 (前 500 字符):")
        print("-" * 50)
        print(ddl_content[:500] + "..." if len(ddl_content) > 500 else ddl_content)

if __name__ == "__main__":
    main()
