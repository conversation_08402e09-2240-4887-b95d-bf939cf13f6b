"""
MySQL DDL 提取工具
=================

从 MySQL 数据库提取 DDL 用于 Vanna 训练
"""

def extract_mysql_ddl():
    """从 MySQL 提取 DDL"""
    
    try:
        import pymysql
    except ImportError:
        print("❌ 需要安装 pymysql: pip install pymysql")
        return None
    
    print("🔗 MySQL DDL 提取工具")
    print("=" * 30)
    
    # 获取连接信息
    host = input("主机地址 (默认: localhost): ").strip() or "localhost"
    port = input("端口 (默认: 3306): ").strip() or "3306"
    user = input("用户名: ").strip()
    password = input("密码: ").strip()
    database = input("数据库名: ").strip()
    
    if not all([user, password, database]):
        print("❌ 用户名、密码和数据库名不能为空")
        return None
    
    try:
        print("🔄 连接到 MySQL...")
        
        connection = pymysql.connect(
            host=host,
            user=user,
            password=password,
            database=database,
            port=int(port),
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 获取所有表名
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        
        if not tables:
            print("⚠️ 数据库中没有表")
            return None
        
        print(f"📊 找到 {len(tables)} 个表:")
        for table in tables:
            print(f"   - {table}")
        
        # 构建 DDL 内容
        ddl_content = f"-- MySQL DDL for database: {database}\n"
        ddl_content += f"-- Host: {host}:{port}\n"
        ddl_content += f"-- Generated on: {__import__('datetime').datetime.now()}\n\n"
        
        # 为每个表生成 DDL
        for table in tables:
            print(f"📋 处理表: {table}")
            
            # 获取建表语句
            cursor.execute(f"SHOW CREATE TABLE `{table}`")
            create_table = cursor.fetchone()[1]
            
            ddl_content += f"-- Table: {table}\n"
            ddl_content += create_table + ";\n\n"
        
        # 保存到文件
        output_file = f"{database}_ddl.sql"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(ddl_content)
        
        connection.close()
        
        print(f"✅ DDL 已保存到: {output_file}")
        return ddl_content, output_file
        
    except Exception as e:
        print(f"❌ 提取 DDL 失败: {e}")
        return None

def create_vanna_training_script(ddl_content, database_name):
    """创建 Vanna 训练脚本"""
    
    if not ddl_content:
        return
    
    training_script = f'''"""
使用 {database_name} DDL 训练 Vanna
===============================
"""

def train_vanna_with_mysql_ddl(vn):
    """使用 MySQL DDL 训练 Vanna"""
    
    print("🎓 使用 {database_name} DDL 训练 Vanna...")
    
    # 完整的 DDL 内容
    mysql_ddl = """''' + ddl_content.replace('"""', '\\"\\"\\"') + '''"""
    
    # 方法 1: 一次性训练所有 DDL
    # vn.train(ddl=mysql_ddl)
    
    # 方法 2: 分表训练（推荐）
    ddl_statements = []
    current_statement = ""
    
    for line in mysql_ddl.split('\\n'):
        if line.strip() and not line.startswith('--'):
            current_statement += line + "\\n"
            if line.strip().endswith(';'):
                ddl_statements.append(current_statement.strip())
                current_statement = ""
    
    print(f"📊 分别训练 {{len(ddl_statements)}} 个 DDL 语句...")
    
    for i, ddl in enumerate(ddl_statements, 1):
        if ddl.upper().startswith('CREATE TABLE'):
            # 提取表名
            table_name = ddl.split('(')[0].replace('CREATE TABLE', '').strip().strip('`')
            print(f"   {{i}}. 训练表: {{table_name}}")
            vn.train(ddl=ddl)
    
    # 添加 MySQL 特定的业务文档
    vn.train(documentation="""
        这是一个 MySQL 数据库：
        - 使用标准 SQL 语法
        - 字符串使用单引号
        - 日期格式: 'YYYY-MM-DD'
        - 时间格式: 'YYYY-MM-DD HH:MM:SS'
        - 支持 LIMIT 语法限制结果数量
        - 支持 JOIN、GROUP BY、ORDER BY 等操作
    """)
    
    print("✅ MySQL DDL 训练完成！")

# 使用示例
if __name__ == "__main__":
    # 假设你已经初始化了 vn
    # train_vanna_with_mysql_ddl(vn)
    print("请在你的 Vanna 脚本中调用 train_vanna_with_mysql_ddl(vn)")
'''
    
    # 保存训练脚本
    script_file = f"{database_name}_vanna_training.py"
    with open(script_file, 'w', encoding='utf-8') as f:
        f.write(training_script)
    
    print(f"✅ Vanna 训练脚本已保存到: {script_file}")

def generate_mysql_connection_example(host, port, user, database):
    """生成 MySQL 连接示例代码"""
    
    example_code = f'''"""
MySQL 连接示例
=============
"""

# 在你的 Vanna 脚本中使用以下代码连接到 MySQL:

vn.connect_to_mysql(
    host='{host}',
    dbname='{database}',
    user='{user}',
    password='your_password',  # 替换为实际密码
    port={port}
)

print("✅ MySQL 数据库连接成功！")

# 然后使用训练脚本:
from {database}_vanna_training import train_vanna_with_mysql_ddl
train_vanna_with_mysql_ddl(vn)

# 开始查询:
result = vn.ask("你的问题")
print(result)
'''
    
    with open(f"{database}_connection_example.py", 'w', encoding='utf-8') as f:
        f.write(example_code)
    
    print(f"✅ 连接示例已保存到: {database}_connection_example.py")

def main():
    """主函数"""
    print("🗄️ MySQL DDL 提取工具")
    print("=" * 40)
    
    # 提取 DDL
    result = extract_mysql_ddl()
    
    if result:
        ddl_content, output_file = result
        
        # 从文件名提取数据库名
        database_name = output_file.replace('_ddl.sql', '')
        
        # 创建训练脚本
        create_vanna_training_script(ddl_content, database_name)
        
        # 生成连接示例
        print("\\n📝 生成连接示例...")
        host = input("主机地址 (用于示例): ").strip() or "localhost"
        port = input("端口 (用于示例): ").strip() or "3306"
        user = input("用户名 (用于示例): ").strip()
        
        if user:
            generate_mysql_connection_example(host, port, user, database_name)
        
        print("\\n🎯 使用方法:")
        print("=" * 20)
        print(f"1. DDL 文件: {output_file}")
        print(f"2. 训练脚本: {database_name}_vanna_training.py")
        print(f"3. 连接示例: {database_name}_connection_example.py")
        
        print("\\n🚀 下一步:")
        print("1. 运行: python vanna_deepseek_mysql.py")
        print("2. 或者在你的脚本中使用生成的训练代码")

if __name__ == "__main__":
    main()
