"""
确保权限正确的 Web 界面启动器
============================

专门解决 allow_llm_to_see_data 权限问题
"""

import socket
import webbrowser
import threading
import time

def find_available_port(start_port=5000):
    """找到可用端口"""
    for port in range(start_port, start_port + 20):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return port
        except OSError:
            continue
    return None

def create_vanna_with_permissions(deepseek_key):
    """创建带完整权限的 Vanna 实例"""
    
    from vanna.openai import OpenAI_Chat
    from vanna.chromadb import ChromaDB_VectorStore
    from openai import OpenAI
    
    print("🔧 创建 Vanna 实例...")
    
    # 创建 DeepSeek 客户端
    deepseek_client = OpenAI(
        api_key=deepseek_key,
        base_url="https://api.deepseek.com"
    )
    
    # 创建增强的 Vanna 类
    class EnhancedVannaDeepSeek(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            OpenAI_Chat.__init__(self, client=deepseek_client, config=config)
            
            # 在初始化时就设置权限
            self.allow_llm_to_see_data = True
            
        def ask(self, question: str, **kwargs):
            """重写 ask 方法，确保权限设置"""
            # 确保权限始终启用
            self.allow_llm_to_see_data = True
            
            try:
                return super().ask(question, **kwargs)
            except Exception as e:
                if "Summarization can be enabled" in str(e):
                    print("🔧 检测到汇总权限问题，正在修复...")
                    self.allow_llm_to_see_data = True
                    # 重试
                    return super().ask(question, **kwargs)
                else:
                    raise e
        
        def generate_sql(self, question: str, **kwargs):
            """重写 generate_sql 方法，确保权限设置"""
            # 确保权限始终启用
            self.allow_llm_to_see_data = True
            return super().generate_sql(question, **kwargs)
    
    # 初始化配置
    config = {
        'model': 'deepseek-chat',
        'temperature': 0.1,
        'max_tokens': 2000,
    }
    
    vn = EnhancedVannaDeepSeek(config=config)
    
    # 多重确保权限设置
    vn.allow_llm_to_see_data = True
    
    print("✅ Vanna 实例创建成功")
    print(f"🔒 权限设置: allow_llm_to_see_data = {vn.allow_llm_to_see_data}")
    
    return vn

def setup_training(vn):
    """设置训练数据"""
    
    print("🎓 设置训练数据...")
    
    # 核心业务文档
    vn.train(documentation="""
        项目管理数据库核心信息：
        
        主要表：sys_project_info (工程项目表)
        重要字段：
        - del: 删除标记 (0=有效, 1=已删除)
        - status: 项目状态 (0=未验, 1=已验, 2=已审)
        - audit_status: 审计标记 (1=需要审计)
        - mtime: 提交时间
        - total: 总费用
        - pname: 项目名称
        - construction: 施工单位
        
        查询规则：
        - 统计工程数量必须加 WHERE del = 0
        - 时间查询使用 mtime 字段
        - 审计查询使用 audit_status 字段
    """)
    
    # 核心查询示例
    training_examples = [
        ("库中一共多少工程", "SELECT COUNT(*) FROM sys_project_info WHERE del = 0"),
        ("工程总数", "SELECT COUNT(*) FROM sys_project_info WHERE del = 0"),
        ("有效工程数量", "SELECT COUNT(*) FROM sys_project_info WHERE del = 0"),
        ("需要审计的工程", "SELECT COUNT(*) FROM sys_project_info WHERE audit_status = 1 AND del = 0"),
        ("已审计工程数量", "SELECT COUNT(*) FROM sys_project_info WHERE audit_status = 1 AND del = 0"),
        ("2020年以后的工程", "SELECT COUNT(*) FROM sys_project_info WHERE mtime >= '2020-01-01' AND del = 0"),
        ("已验收工程数量", "SELECT COUNT(*) FROM sys_project_info WHERE status >= 1 AND del = 0"),
        ("工程总费用", "SELECT SUM(total) FROM sys_project_info WHERE del = 0"),
        ("平均工程费用", "SELECT AVG(total) FROM sys_project_info WHERE del = 0 AND total > 0"),
    ]
    
    for question, sql in training_examples:
        vn.train(question=question, sql=sql)
    
    print(f"✅ 已添加 {len(training_examples)} 个训练示例")

def main():
    """主函数"""
    print("🌐 权限修复版 Web 界面启动器")
    print("=" * 50)
    
    print("🔒 此版本专门解决以下问题:")
    print("   - allow_llm_to_see_data 权限问题")
    print("   - Summarization can be enabled 错误")
    print("   - 数据汇总功能问题")
    
    # 使用之前测试成功的配置
    print("\n💡 使用之前测试成功的配置")
    deepseek_key = "***********************************"
    mysql_password = "22223333"

    print("✅ 配置已加载")
    
    try:
        # 创建 Vanna 实例
        vn = create_vanna_with_permissions(deepseek_key)
        
        # 连接数据库
        print("🔗 连接到 MySQL...")
        vn.connect_to_mysql(
            host='***********',
            dbname='project',
            user='root',
            password=mysql_password,
            port=3306
        )
        print("✅ MySQL 连接成功")
        
        # 设置训练数据
        setup_training(vn)
        
        # 测试权限
        print("🧪 测试权限设置...")
        try:
            test_result = vn.ask("库中一共多少工程")
            print("✅ 权限测试成功")
        except Exception as e:
            print(f"⚠️ 权限测试警告: {e}")
            print("🔧 继续启动 Web 界面...")
        
        # 启动 Web 界面
        from vanna.flask import VannaFlaskApp
        
        port = find_available_port()
        if port is None:
            print("❌ 无法找到可用端口")
            return
        
        print(f"🌐 启动 Web 界面在端口 {port}")
        print(f"📱 访问地址: http://localhost:{port}")
        
        # 自动打开浏览器
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🌐 浏览器已自动打开")
            except Exception:
                print(f"请手动访问: http://localhost:{port}")
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        print("\n🎯 权限问题修复完成！")
        print("✅ allow_llm_to_see_data = True")
        print("✅ 数据汇总功能已启用")
        print("✅ 中文查询支持已启用")
        
        print("\n💡 推荐测试查询:")
        print("   - 库中一共多少工程")
        print("   - 需要审计的工程数量")
        print("   - 2020年以后的工程项目")
        print("   - 工程总费用统计")
        
        print("\n⏹️ 按 Ctrl+C 停止服务器")
        
        # 启动应用
        app = VannaFlaskApp(vn)
        app.run(
            host='127.0.0.1',
            port=port,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Web 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
