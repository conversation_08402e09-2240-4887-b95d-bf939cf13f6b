"""
Vanna AI 快速入门指南
===================

Vanna 是一个用于 SQL 生成的 RAG 框架，让你可以用自然语言查询数据库。

安装步骤：
1. pip install vanna
2. 选择你的 LLM 和向量数据库组合
3. 训练模型
4. 开始提问

本文件包含多个示例，展示不同的使用方式。
"""

# ============================================================================
# 示例 1: 使用 Vanna 的远程服务（最简单的开始方式）
# ============================================================================

def example_1_remote_service():
    """使用 Vanna 的远程服务和示例数据"""
    print("=== 示例 1: 使用远程服务 ===")
    
    import vanna
    from vanna.remote import VannaDefault
    
    # 使用预训练的 Chinook 数据库模型
    vn = VannaDefault(model='chinook', api_key=vanna.get_api_key('<EMAIL>'))
    
    # 连接到示例 SQLite 数据库
    vn.connect_to_sqlite('https://vanna.ai/Chinook.sqlite')
    
    # 开始提问
    result = vn.ask("What are the top 10 albums by sales?")
    print("SQL 查询结果:")
    print(result)


# ============================================================================
# 示例 2: 本地设置 - OpenAI + ChromaDB
# ============================================================================

def example_2_local_openai_chromadb():
    """本地设置使用 OpenAI 和 ChromaDB"""
    print("=== 示例 2: 本地 OpenAI + ChromaDB ===")
    
    from vanna.openai.openai_chat import OpenAI_Chat
    from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
    
    class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            OpenAI_Chat.__init__(self, config=config)
    
    # 初始化（需要 OpenAI API 密钥）
    vn = MyVanna(config={
        'api_key': 'sk-your-openai-api-key-here',  # 替换为你的 OpenAI API 密钥
        'model': 'gpt-4'
    })
    
    # 训练模型 - 添加数据库结构信息
    vn.train(ddl="""
        CREATE TABLE customers (
            id INT PRIMARY KEY,
            name VARCHAR(100),
            email VARCHAR(100),
            created_at TIMESTAMP
        );
        
        CREATE TABLE orders (
            id INT PRIMARY KEY,
            customer_id INT,
            total_amount DECIMAL(10,2),
            order_date DATE,
            FOREIGN KEY (customer_id) REFERENCES customers(id)
        );
    """)
    
    # 训练模型 - 添加业务文档
    vn.train(documentation="我们的业务将客户定义为购买过产品的用户。")
    
    # 训练模型 - 添加示例 SQL 查询
    vn.train(sql="SELECT name, email FROM customers WHERE created_at > '2023-01-01'")
    
    # 提问
    sql = vn.generate_sql("显示销售额最高的前 5 个客户")
    print("生成的 SQL:")
    print(sql)


# ============================================================================
# 示例 3: 使用 Ollama（本地 LLM）
# ============================================================================

def example_3_ollama_local():
    """使用本地 Ollama LLM"""
    print("=== 示例 3: 使用 Ollama 本地 LLM ===")
    
    from vanna.ollama import Ollama
    from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
    
    class MyLocalVanna(ChromaDB_VectorStore, Ollama):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            Ollama.__init__(self, config=config)
    
    # 初始化（需要先安装并运行 Ollama）
    vn = MyLocalVanna(config={
        'model': 'llama2',  # 或其他你安装的模型
        'ollama_host': 'http://localhost:11434'
    })
    
    print("注意：需要先安装并运行 Ollama")
    print("安装命令：curl -fsSL https://ollama.ai/install.sh | sh")
    print("运行模型：ollama run llama2")


# ============================================================================
# 示例 4: 连接到真实数据库
# ============================================================================

def example_4_real_database():
    """连接到真实数据库的示例"""
    print("=== 示例 4: 连接真实数据库 ===")
    
    from vanna.openai.openai_chat import OpenAI_Chat
    from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
    
    class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            OpenAI_Chat.__init__(self, config=config)
    
    vn = MyVanna(config={'api_key': 'your-openai-key', 'model': 'gpt-4'})
    
    # 连接到不同类型的数据库
    
    # PostgreSQL
    # vn.connect_to_postgres(host='localhost', dbname='mydb', user='user', password='password', port=5432)
    
    # MySQL
    # vn.connect_to_mysql(host='localhost', dbname='mydb', user='user', password='password', port=3306)
    
    # SQLite
    # vn.connect_to_sqlite('path/to/your/database.db')
    
    # Snowflake
    # vn.connect_to_snowflake(
    #     account='your-account',
    #     username='your-username',
    #     password='your-password',
    #     database='your-database',
    #     schema='your-schema'
    # )
    
    print("数据库连接示例（需要根据实际情况修改连接参数）")


# ============================================================================
# 示例 5: 启动 Web 界面
# ============================================================================

def example_5_web_interface():
    """启动 Web 界面"""
    print("=== 示例 5: Web 界面 ===")
    
    from vanna.openai.openai_chat import OpenAI_Chat
    from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
    from vanna.flask import VannaFlaskApp
    
    class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            OpenAI_Chat.__init__(self, config=config)
    
    vn = MyVanna(config={'api_key': 'your-openai-key', 'model': 'gpt-4'})
    
    # 启动 Flask Web 应用
    try:
        import socket

        # 找到可用端口
        def find_available_port(start_port=5000):
            for port in range(start_port, start_port + 10):
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.bind(('127.0.0.1', port))
                        return port
                except OSError:
                    continue
            return None

        port = find_available_port()
        if port:
            print(f"🌐 启动 Web 界面在端口 {port}")
            print(f"📱 访问地址: http://localhost:{port}")
            print("⏹️ 按 Ctrl+C 停止服务器")

            app = VannaFlaskApp(vn)
            app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)
        else:
            print("❌ 无法找到可用端口")
            print("💡 建议使用 Jupyter Notebook: python vanna_notebook_demo.py")
    except Exception as e:
        print(f"❌ Web 应用启动失败: {e}")
        print("💡 建议使用 Jupyter Notebook: python vanna_notebook_demo.py")


# ============================================================================
# 完整的工作流程示例
# ============================================================================

def complete_workflow_example():
    """完整的 Vanna 工作流程"""
    print("=== 完整工作流程示例 ===")
    
    # 步骤 1: 安装和导入
    print("1. 安装: pip install vanna")
    
    # 步骤 2: 设置
    from vanna.openai.openai_chat import OpenAI_Chat
    from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
    
    class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            OpenAI_Chat.__init__(self, config=config)
    
    # 步骤 3: 初始化
    # vn = MyVanna(config={'api_key': 'your-key', 'model': 'gpt-4'})
    
    # 步骤 4: 连接数据库
    # vn.connect_to_sqlite('your_database.db')
    
    # 步骤 5: 训练模型
    training_examples = [
        # DDL 训练
        """
        CREATE TABLE products (
            id INT PRIMARY KEY,
            name VARCHAR(255),
            price DECIMAL(10,2),
            category_id INT
        );
        """,
        
        # 文档训练
        "产品类别包括：电子产品、服装、家居用品等。",
        
        # SQL 示例训练
        "SELECT name, price FROM products WHERE price > 100 ORDER BY price DESC;"
    ]
    
    print("5. 训练示例:")
    for i, example in enumerate(training_examples, 1):
        print(f"   训练 {i}: {example[:50]}...")
    
    # 步骤 6: 提问
    questions = [
        "显示价格最高的 10 个产品",
        "每个类别有多少个产品？",
        "平均产品价格是多少？"
    ]
    
    print("6. 示例问题:")
    for question in questions:
        print(f"   - {question}")


# ============================================================================
# 主函数
# ============================================================================

if __name__ == "__main__":
    print("Vanna AI 快速入门指南")
    print("=" * 50)
    
    print("\n选择要运行的示例:")
    print("1. 远程服务示例（最简单）")
    print("2. 本地 OpenAI + ChromaDB")
    print("3. 本地 Ollama LLM")
    print("4. 真实数据库连接")
    print("5. Web 界面")
    print("6. 完整工作流程说明")
    
    choice = input("\n请输入选择 (1-6): ")
    
    if choice == "1":
        example_1_remote_service()
    elif choice == "2":
        example_2_local_openai_chromadb()
    elif choice == "3":
        example_3_ollama_local()
    elif choice == "4":
        example_4_real_database()
    elif choice == "5":
        example_5_web_interface()
    elif choice == "6":
        complete_workflow_example()
    else:
        print("无效选择，显示完整工作流程:")
        complete_workflow_example()
