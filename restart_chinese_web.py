"""
重启中文 Web 界面
================

快速重启 Web 界面并应用中文配置
"""

import subprocess
import sys
import time

def restart_web_with_chinese():
    """重启 Web 界面并应用中文配置"""
    
    print("🔄 重启 Web 界面（中文配置）")
    print("=" * 40)
    
    print("🛑 如果有正在运行的 Web 服务器，请先按 Ctrl+C 停止")
    
    # 等待用户确认
    input("按 Enter 继续启动中文版 Web 界面...")
    
    print("🚀 启动中文版 Web 界面...")
    
    try:
        # 运行更新后的 Web 启动脚本
        subprocess.run([sys.executable, "start_web_mysql.py"], cwd=".")
    except KeyboardInterrupt:
        print("\n👋 Web 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def show_chinese_config_info():
    """显示中文配置信息"""
    
    print("🇨🇳 中文配置说明")
    print("=" * 30)
    
    print("✅ 已配置的中文功能:")
    print("   1. 中文问题理解")
    print("   2. 中文结果解释")
    print("   3. 中文错误提示")
    print("   4. 专业的数据分析")
    print("   5. 业务术语翻译")
    
    print("\n💡 中文查询示例:")
    examples = [
        "库中一共多少工程",
        "需要审计的工程数量",
        "2020年以后的工程项目",
        "费用最高的10个工程",
        "各个设计单位的工程数量",
        "已验收但未审计的工程",
        "本月提交的工程有多少",
        "平均每个工程的费用",
        "按年度统计工程数量",
        "总费用超过10万的工程"
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"   {i:2d}. {example}")
    
    print("\n🎯 预期改进:")
    print("   - 更自然的中文对话")
    print("   - 更准确的业务理解")
    print("   - 更专业的数据分析")
    print("   - 更清晰的结果解释")

def test_chinese_config():
    """测试中文配置"""
    
    print("🧪 测试中文配置")
    print("=" * 20)
    
    try:
        from chinese_vanna_config import setup_chinese_vanna_deepseek
        print("✅ 中文配置模块可用")
        
        # 显示配置特性
        print("📋 中文配置特性:")
        print("   - 自定义中文系统提示")
        print("   - 中文查询示例训练")
        print("   - 中文结果解释")
        print("   - 专业术语处理")
        
        return True
        
    except ImportError as e:
        print(f"❌ 中文配置模块不可用: {e}")
        return False

def main():
    """主函数"""
    print("🇨🇳 中文 Web 界面重启工具")
    print("=" * 40)
    
    # 显示配置信息
    show_chinese_config_info()
    
    # 测试配置
    if test_chinese_config():
        print("\n🚀 准备重启 Web 界面...")
        restart_web_with_chinese()
    else:
        print("\n❌ 中文配置不可用")
        print("💡 请确保 chinese_vanna_config.py 文件存在")

if __name__ == "__main__":
    main()
