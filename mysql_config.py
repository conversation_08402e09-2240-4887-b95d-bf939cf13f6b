"""
MySQL 连接配置
=============

根据测试结果生成的连接配置
"""

# MySQL 连接信息
MYSQL_CONFIG = {
    'host': '***********',
    'port': 3306,
    'user': 'root',
    'password': '22223333',  # 注意：实际使用时请妥善保管密码
    'database': 'project'
}

# 在 Vanna 中使用
def connect_vanna_to_mysql(vn):
    """连接 Vanna 到 MySQL"""
    vn.connect_to_mysql(
        host=MYSQL_CONFIG['host'],
        dbname=MYSQL_CONFIG['database'],
        user=MYSQL_CONFIG['user'],
        password=MYSQL_CONFIG['password'],
        port=MYSQL_CONFIG['port']
    )
    print("✅ Vanna MySQL 连接成功！")

# 数据库表信息
TABLES = ['0_sys_user_backup20220209', 'company', 'sys_project_info', 'sys_purchase', 'sys_user']

print(f"数据库 '{MYSQL_CONFIG['database']}' 包含 {len(TABLES)} 个表:")
for table in TABLES:
    print(f"  - {table}")
