# Vanna AI 入门指南

Vanna 是一个开源的 Python RAG 框架，让你可以用自然语言查询 SQL 数据库。

## 🚀 快速开始

### 1. 安装 Vanna

```bash
pip install vanna
```

### 2. 最简单的开始方式

运行我们提供的简单示例：

```bash
python simple_start.py
```

这将使用 Vanna 的远程服务和示例数据库，让你立即体验功能。

### 3. 本地设置

如果你想使用自己的数据库和 LLM，可以参考 `vanna_config_examples.py` 中的配置示例。

## 📋 Vanna 工作原理

Vanna 分为两个步骤：

1. **训练阶段** (`vn.train(...)`): 向模型提供数据库结构、文档和示例查询
2. **查询阶段** (`vn.ask(...)`): 用自然语言提问，获得 SQL 查询和结果

## 🔧 支持的组件

### LLM 提供商
- OpenAI (GPT-4, GPT-3.5)
- Azure OpenAI
- Anthropic Claude
- Google Gemini
- Ollama (本地)
- HuggingFace
- AWS Bedrock

### 向量数据库
- ChromaDB (推荐开始使用)
- Qdrant
- Pinecone
- Weaviate
- FAISS
- Milvus

### 支持的数据库
- PostgreSQL
- MySQL
- SQLite
- Snowflake
- BigQuery
- Oracle
- Microsoft SQL Server
- DuckDB

## 📝 基本使用流程

### 1. 设置和初始化

```python
from vanna.openai.openai_chat import OpenAI_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore

class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, config=config)

vn = MyVanna(config={'api_key': 'your-openai-key', 'model': 'gpt-4'})
```

### 2. 连接数据库

```python
# SQLite
vn.connect_to_sqlite('your_database.db')

# PostgreSQL
vn.connect_to_postgres(host='localhost', dbname='mydb', user='user', password='pass')
```

### 3. 训练模型

```python
# 添加数据库结构
vn.train(ddl="CREATE TABLE users (id INT, name VARCHAR(100), email VARCHAR(100))")

# 添加业务文档
vn.train(documentation="用户表包含所有注册用户的信息")

# 添加示例查询
vn.train(sql="SELECT name, email FROM users WHERE created_at > '2023-01-01'")
```

### 4. 开始查询

```python
# 生成 SQL
sql = vn.generate_sql("显示所有用户的姓名和邮箱")

# 执行查询
result = vn.run_sql(sql)

# 一步完成
answer = vn.ask("有多少个用户？")
```

## 🌐 Web 界面

Vanna 提供了多种 Web 界面选项：

### Flask 应用
```python
from vanna.flask import VannaFlaskApp
app = VannaFlaskApp(vn)
app.run()
```

### Streamlit 应用
```bash
pip install vanna[streamlit]
```

## 📁 文件说明

- `simple_start.py` - 最简单的入门示例，使用远程服务
- `vanna_quickstart.py` - 详细的示例集合，包含多种使用方式
- `vanna_config_examples.py` - 不同 LLM 和向量数据库的配置示例

## 🎯 下一步

1. 运行 `simple_start.py` 体验基本功能
2. 查看 `vanna_quickstart.py` 了解更多选项
3. 根据你的需求选择合适的 LLM 和向量数据库配置
4. 连接到你自己的数据库
5. 训练模型并开始查询！

## 🔗 有用链接

- [官方文档](https://vanna.ai/docs/)
- [GitHub 仓库](https://github.com/vanna-ai/vanna)
- [示例 Colab Notebook](https://colab.research.google.com/drive/1ACa9QKzxVINHSgpzrR5CUOUoM3QyOUaf)

## 💡 提示

- 从简单的示例数据开始学习
- 训练数据的质量直接影响查询准确性
- 可以逐步添加更多训练数据来提高性能
- 支持多种前端界面，选择最适合你的工作流程的
