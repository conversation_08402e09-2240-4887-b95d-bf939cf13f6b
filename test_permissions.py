"""
测试 Vanna 权限设置
==================

验证 allow_llm_to_see_data 权限是否正确设置
"""

def test_vanna_permissions():
    """测试 Vanna 权限设置"""
    
    print("🧪 测试 Vanna 权限设置")
    print("=" * 40)
    
    try:
        from vanna.openai import OpenAI_Chat
        from vanna.chromadb import ChromaDB_VectorStore
        from openai import OpenAI
        
        # 获取配置信息
        deepseek_key = input("请输入 DeepSeek API 密钥: ").strip()
        mysql_password = input("请输入 MySQL 密码: ").strip()
        
        if not deepseek_key or not mysql_password:
            print("❌ 需要提供完整的配置信息")
            return False
        
        print("🔧 创建 Vanna 实例...")
        
        # 创建 DeepSeek 客户端
        deepseek_client = OpenAI(
            api_key=deepseek_key,
            base_url="https://api.deepseek.com"
        )
        
        # 创建 Vanna 类
        class VannaDeepSeek(ChromaDB_VectorStore, OpenAI_Chat):
            def __init__(self, config=None):
                ChromaDB_VectorStore.__init__(self, config=config)
                OpenAI_Chat.__init__(self, client=deepseek_client, config=config)
        
        # 初始化
        vn = VannaDeepSeek(config={
            'model': 'deepseek-chat',
            'temperature': 0.1,
            'max_tokens': 2000,
        })
        
        print("✅ Vanna 实例创建成功")
        
        # 检查默认权限设置
        print(f"🔍 默认权限设置: allow_llm_to_see_data = {getattr(vn, 'allow_llm_to_see_data', 'undefined')}")
        
        # 显式设置权限
        vn.allow_llm_to_see_data = True
        print("🔧 已设置: allow_llm_to_see_data = True")
        
        # 验证权限设置
        print(f"✅ 当前权限设置: allow_llm_to_see_data = {vn.allow_llm_to_see_data}")
        
        # 连接数据库
        print("🔗 连接到 MySQL 数据库...")
        vn.connect_to_mysql(
            host='***********',
            dbname='project',
            user='root',
            password=mysql_password,
            port=3306
        )
        print("✅ MySQL 连接成功")
        
        # 测试基本查询
        print("🧪 测试基本查询...")
        try:
            result = vn.run_sql("SELECT COUNT(*) as total FROM sys_project_info WHERE del = 0")
            if result is not None and not result.empty:
                total = result.iloc[0, 0]
                print(f"✅ 查询成功: 总工程数 = {total}")
            else:
                print("⚠️ 查询无结果")
        except Exception as e:
            print(f"❌ 查询失败: {e}")
        
        # 测试 SQL 生成
        print("🧪 测试 SQL 生成...")
        try:
            # 添加基本训练数据
            vn.train(documentation="sys_project_info 是工程项目表，del=0表示有效记录")
            vn.train(question="工程总数", sql="SELECT COUNT(*) FROM sys_project_info WHERE del = 0")
            
            sql = vn.generate_sql("库中一共多少工程")
            print(f"✅ SQL 生成成功: {sql}")
        except Exception as e:
            print(f"❌ SQL 生成失败: {e}")
        
        # 测试完整查询（包括汇总）
        print("🧪 测试完整查询（包括汇总）...")
        try:
            response = vn.ask("库中一共多少工程")
            print("✅ 完整查询成功")
            print(f"📊 响应: {response}")
        except Exception as e:
            print(f"❌ 完整查询失败: {e}")
            if "Summarization can be enabled" in str(e):
                print("🔧 这是汇总权限问题，正在修复...")
                # 确保权限设置
                vn.allow_llm_to_see_data = True
                try:
                    response = vn.ask("库中一共多少工程")
                    print("✅ 修复后查询成功")
                    print(f"📊 响应: {response}")
                except Exception as e2:
                    print(f"❌ 修复后仍然失败: {e2}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_permission_info():
    """显示权限信息"""
    
    print("📚 Vanna 权限说明")
    print("=" * 30)
    
    print("🔒 allow_llm_to_see_data 权限控制:")
    print("   - False (默认): LLM 无法查看数据库数据")
    print("   - True: LLM 可以查看数据库数据和结构")
    
    print("\n✅ 启用此权限的好处:")
    print("   1. 可以生成更准确的 SQL")
    print("   2. 可以进行数据汇总和分析")
    print("   3. 可以提供更详细的查询结果解释")
    print("   4. 可以进行数据验证和错误检查")
    
    print("\n⚠️ 安全考虑:")
    print("   - 只在信任的环境中启用")
    print("   - LLM 可能会看到敏感数据")
    print("   - 建议在生产环境中谨慎使用")

def main():
    """主函数"""
    print("🔧 Vanna 权限测试工具")
    print("=" * 50)
    
    # 显示权限信息
    show_permission_info()
    
    # 询问是否继续测试
    test_choice = input("\n是否进行权限测试? (y/n): ").lower()
    if test_choice == 'y':
        if test_vanna_permissions():
            print("\n🎉 权限测试完成！")
            print("💡 如果测试成功，Web 界面应该可以正常工作")
        else:
            print("\n❌ 权限测试失败")
            print("💡 请检查配置和网络连接")

if __name__ == "__main__":
    main()
