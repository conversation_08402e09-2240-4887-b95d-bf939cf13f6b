# 🗄️ 数据库 DDL 生成命令大全

## 📋 命令行工具方式

### 1. **MySQL**

```bash
# 方法 1: 使用 mysqldump
mysqldump -h localhost -u username -p --no-data --routines --triggers database_name > mysql_ddl.sql

# 方法 2: 只导出表结构
mysqldump -h localhost -u username -p -d database_name > mysql_schema.sql

# 方法 3: 导出特定表
mysqldump -h localhost -u username -p --no-data database_name table1 table2 > specific_tables.sql
```

### 2. **PostgreSQL**

```bash
# 方法 1: 使用 pg_dump
pg_dump -h localhost -U username -d database_name --schema-only > postgresql_ddl.sql

# 方法 2: 只导出表结构
pg_dump -h localhost -U username -d database_name -s > postgresql_schema.sql

# 方法 3: 导出特定模式
pg_dump -h localhost -U username -d database_name -n schema_name --schema-only > schema_ddl.sql
```

### 3. **SQL Server**

```bash
# 使用 sqlcmd (需要安装 SQL Server 命令行工具)
sqlcmd -S server_name -d database_name -E -Q "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES" -o tables.txt

# 或使用 PowerShell
# 需要安装 SqlServer 模块: Install-Module -Name SqlServer
```

### 4. **SQLite**

```bash
# 方法 1: 使用 sqlite3 命令
sqlite3 database.db ".schema" > sqlite_ddl.sql

# 方法 2: 导出特定表
sqlite3 database.db ".schema table_name" > table_ddl.sql

# 方法 3: 完整的数据库结构
sqlite3 database.db ".dump" | grep "CREATE" > sqlite_schema.sql
```

## 🐍 Python 脚本方式

### 快速使用我们的工具

```bash
# 运行 DDL 生成工具
python generate_ddl.py
```

### 或者直接在代码中使用

```python
# SQLite 示例
import sqlite3

def get_sqlite_ddl(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 获取所有建表语句
    cursor.execute("SELECT sql FROM sqlite_master WHERE type='table'")
    ddl_statements = [row[0] for row in cursor.fetchall() if row[0]]
    
    conn.close()
    return ";\n\n".join(ddl_statements) + ";"

# 使用
ddl = get_sqlite_ddl("your_database.db")
print(ddl)
```

## 🔧 在线工具

### 1. **phpMyAdmin** (MySQL)
- 选择数据库 → 导出 → 结构 → 执行

### 2. **pgAdmin** (PostgreSQL)  
- 右键数据库 → Backup → 只选择 Schema

### 3. **SQL Server Management Studio**
- 右键数据库 → 任务 → 生成脚本 → 只编写架构脚本

## 🎯 为 Vanna 优化的 DDL

### 清理和优化 DDL

```python
def clean_ddl_for_vanna(ddl_content):
    """清理 DDL 内容，使其更适合 Vanna"""
    lines = ddl_content.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # 移除注释
        if line.strip().startswith('--') or line.strip().startswith('/*'):
            continue
        
        # 移除空行
        if not line.strip():
            continue
            
        # 移除数据库特定的语法
        if any(keyword in line.upper() for keyword in ['ENGINE=', 'CHARSET=', 'COLLATE=']):
            # 简化 MySQL 特定语法
            line = line.split('ENGINE=')[0].rstrip(' ,')
        
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

# 使用示例
with open('mysql_ddl.sql', 'r') as f:
    raw_ddl = f.read()

cleaned_ddl = clean_ddl_for_vanna(raw_ddl)

# 用于 Vanna 训练
vn.train(ddl=cleaned_ddl)
```

## 📊 实际使用示例

### 从现有的 Chinook 数据库生成 DDL

```python
# 我们已经有 Chinook.sqlite，让我们生成它的 DDL
import sqlite3

def generate_chinook_ddl():
    conn = sqlite3.connect('Chinook.sqlite')
    cursor = conn.cursor()
    
    cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' ORDER BY name")
    tables = cursor.fetchall()
    
    ddl_content = "-- Chinook Database DDL\n\n"
    
    for table_sql in tables:
        if table_sql[0]:  # 跳过空的 SQL
            ddl_content += table_sql[0] + ";\n\n"
    
    conn.close()
    
    with open('chinook_ddl.sql', 'w', encoding='utf-8') as f:
        f.write(ddl_content)
    
    print("✅ Chinook DDL 已生成: chinook_ddl.sql")
    return ddl_content

# 生成并用于 Vanna
ddl = generate_chinook_ddl()
# vn.train(ddl=ddl)
```

## 💡 最佳实践

### 1. **分表训练**（推荐）
```python
# 不要一次性训练所有表
# 而是分别训练每个表
tables_ddl = ddl_content.split('CREATE TABLE')

for table_ddl in tables_ddl[1:]:  # 跳过第一个空元素
    clean_ddl = 'CREATE TABLE' + table_ddl
    vn.train(ddl=clean_ddl)
```

### 2. **添加业务上下文**
```python
# 在 DDL 之后添加业务说明
vn.train(ddl=table_ddl)
vn.train(documentation=f"表 {table_name} 用于存储...")
```

### 3. **包含关系信息**
```python
# 确保 DDL 包含外键关系
# 这对 Vanna 理解表之间的关系很重要
```

## 🚀 快速开始

1. **选择你的数据库类型**
2. **运行对应的命令或脚本**
3. **清理生成的 DDL**
4. **用于 Vanna 训练**

```bash
# 一键生成和使用
python generate_ddl.py
```

这样你就可以快速从任何数据库生成 DDL，并用于 Vanna 的训练了！
