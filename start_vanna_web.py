"""
启动 Vanna Web 应用
==================

修复端口访问问题的 Vanna Web 应用启动脚本
"""

import socket
import webbrowser
import threading
import time

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            return True
    except OSError:
        return False

def find_available_port(start_port=5000):
    """找到可用端口"""
    for port in range(start_port, start_port + 20):
        if check_port_available(port):
            return port
    return None

def open_browser_delayed(url, delay=3):
    """延迟打开浏览器"""
    def open_browser():
        time.sleep(delay)
        try:
            webbrowser.open(url)
            print(f"🌐 浏览器已打开: {url}")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"请手动访问: {url}")
    
    thread = threading.Thread(target=open_browser, daemon=True)
    thread.start()

def start_vanna_web_remote():
    """使用远程服务启动 Vanna Web 应用"""
    try:
        import vanna
        from vanna.remote import VannaDefault
        from vanna.flask import VannaFlaskApp
        
        print("🚀 启动 Vanna Web 应用 (远程服务)")
        print("=" * 50)
        
        # 获取 API 密钥
        email = input("请输入你的邮箱地址: ").strip()
        if not email:
            print("❌ 需要邮箱地址")
            return
        
        print("📧 获取 API 密钥...")
        try:
            api_key = vanna.get_api_key(email)
            print("✅ API 密钥获取成功")
        except Exception as e:
            print(f"❌ 获取 API 密钥失败: {e}")
            print("💡 请检查邮箱地址是否正确")
            return
        
        # 初始化 Vanna
        print("🔧 初始化 Vanna...")
        vn = VannaDefault(model='chinook', api_key=api_key)
        
        # 连接到示例数据库
        print("🔗 连接到示例数据库...")
        vn.connect_to_sqlite('https://vanna.ai/Chinook.sqlite')
        print("✅ 数据库连接成功！")
        
        # 找到可用端口
        port = find_available_port(5000)
        if port is None:
            print("❌ 无法找到可用端口")
            return
        
        print(f"🌐 启动 Web 服务器在端口 {port}...")
        url = f"http://localhost:{port}"
        print(f"📱 访问地址: {url}")
        
        # 延迟打开浏览器
        open_browser_delayed(url)
        
        print("⏹️ 按 Ctrl+C 停止服务器")
        print("🎯 Web 应用功能:")
        print("   - 自然语言查询数据库")
        print("   - 查看生成的 SQL")
        print("   - 可视化查询结果")
        
        # 启动 Flask 应用
        app = VannaFlaskApp(vn)
        
        # 使用不同的配置来避免端口问题
        app.run(
            host='127.0.0.1',  # 只绑定本地
            port=port,
            debug=False,  # 关闭调试模式
            use_reloader=False,  # 关闭自动重载
            threaded=True  # 启用多线程
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装 vanna: pip install vanna")
    except KeyboardInterrupt:
        print("\n👋 Web 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 可能的解决方案:")
        print("1. 检查防火墙设置")
        print("2. 以管理员身份运行")
        print("3. 使用不同的端口")

def start_vanna_web_local():
    """使用本地配置启动 Web 应用"""
    try:
        from vanna.openai.openai_chat import OpenAI_Chat
        from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
        from vanna.flask import VannaFlaskApp
        import sqlite3
        
        print("🏠 启动 Vanna Web 应用 (本地配置)")
        print("=" * 50)
        
        # 获取 OpenAI API 密钥
        api_key = input("请输入你的 OpenAI API 密钥: ").strip()
        if not api_key:
            print("❌ 需要 OpenAI API 密钥")
            return
        
        # 创建 Vanna 类
        class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
            def __init__(self, config=None):
                ChromaDB_VectorStore.__init__(self, config=config)
                OpenAI_Chat.__init__(self, config=config)
        
        # 初始化
        print("🔧 初始化 Vanna...")
        vn = MyVanna(config={'api_key': api_key, 'model': 'gpt-3.5-turbo'})
        
        # 创建示例数据库
        print("📊 创建示例数据库...")
        if not os.path.exists('demo.db'):
            conn = sqlite3.connect('demo.db')
            cursor = conn.cursor()
            
            cursor.execute("""
                CREATE TABLE products (
                    id INTEGER PRIMARY KEY,
                    name TEXT,
                    category TEXT,
                    price REAL,
                    stock INTEGER
                )
            """)
            
            sample_data = [
                (1, 'iPhone 15', '电子产品', 999.99, 50),
                (2, 'MacBook Pro', '电子产品', 1999.99, 30),
                (3, 'Nike 运动鞋', '服装', 129.99, 100),
                (4, '咖啡机', '家电', 299.99, 25),
                (5, '办公椅', '家具', 199.99, 40),
            ]
            
            cursor.executemany("INSERT INTO products VALUES (?, ?, ?, ?, ?)", sample_data)
            conn.commit()
            conn.close()
        
        # 连接数据库
        vn.connect_to_sqlite('demo.db')
        
        # 训练模型
        print("🎓 训练模型...")
        vn.train(ddl="""
            CREATE TABLE products (
                id INTEGER PRIMARY KEY,
                name TEXT,
                category TEXT,
                price REAL,
                stock INTEGER
            );
        """)
        
        vn.train(documentation="产品表包含商店的产品信息。")
        print("✅ 训练完成！")
        
        # 启动 Web 应用
        port = find_available_port(5000)
        if port is None:
            print("❌ 无法找到可用端口")
            return
        
        print(f"🌐 启动 Web 服务器在端口 {port}...")
        url = f"http://localhost:{port}"
        print(f"📱 访问地址: {url}")
        
        open_browser_delayed(url)
        
        app = VannaFlaskApp(vn)
        app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("可能需要安装: pip install 'vanna[chromadb,openai]'")
    except KeyboardInterrupt:
        print("\n👋 Web 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🌐 Vanna Web 应用启动器")
    print("=" * 40)
    
    print("选择启动方式:")
    print("1. 远程服务 (推荐，使用示例数据)")
    print("2. 本地配置 (需要 OpenAI API 密钥)")
    print("3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        start_vanna_web_remote()
    elif choice == "2":
        start_vanna_web_local()
    elif choice == "3":
        print("👋 再见！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
