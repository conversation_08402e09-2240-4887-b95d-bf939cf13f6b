"""
创建 DeepSeek + <PERSON><PERSON> Jupyter Notebook
====================================
"""

def create_deepseek_notebook():
    """创建 DeepSeek 专用的 Jupyter Notebook"""
    
    notebook_content = '''{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Vanna + DeepSeek 演示\\n",
    "\\n",
    "使用 DeepSeek API 作为 LLM 提供商的 Vanna AI 演示"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. 安装依赖"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 安装必要的包\\n",
    "!pip install vanna openai\\n",
    "\\n",
    "import vanna\\n",
    "from vanna.openai import OpenAI_Chat\\n",
    "from vanna.chromadb import ChromaDB_VectorStore\\n",
    "from openai import OpenAI\\n",
    "import pandas as pd\\n",
    "import sqlite3"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. 配置 DeepSeek"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 配置 DeepSeek API\\n",
    "DEEPSEEK_API_KEY = \\"your-deepseek-api-key-here\\"  # 替换为你的 API 密钥\\n",
    "\\n",
    "# 创建 DeepSeek 客户端\\n",
    "deepseek_client = OpenAI(\\n",
    "    api_key=DEEPSEEK_API_KEY,\\n",
    "    base_url=\\"https://api.deepseek.com\\"\\n",
    ")\\n",
    "\\n",
    "print('✅ DeepSeek 客户端创建成功')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. 创建 Vanna + DeepSeek 类"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class VannaDeepSeek(ChromaDB_VectorStore, OpenAI_Chat):\\n",
    "    def __init__(self, config=None):\\n",
    "        ChromaDB_VectorStore.__init__(self, config=config)\\n",
    "        # 使用 DeepSeek 客户端\\n",
    "        OpenAI_Chat.__init__(self, client=deepseek_client, config=config)\\n",
    "\\n",
    "# 初始化 Vanna\\n",
    "vn = VannaDeepSeek(config={'model': 'deepseek-chat'})\\n",
    "\\n",
    "print('✅ Vanna + DeepSeek 初始化成功！')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. 创建示例数据库"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 创建示例数据库\\n",
    "conn = sqlite3.connect('company.db')\\n",
    "cursor = conn.cursor()\\n",
    "\\n",
    "# 员工表\\n",
    "cursor.execute('''\\n",
    "    CREATE TABLE IF NOT EXISTS employees (\\n",
    "        id INTEGER PRIMARY KEY,\\n",
    "        name TEXT NOT NULL,\\n",
    "        department TEXT NOT NULL,\\n",
    "        salary REAL NOT NULL,\\n",
    "        hire_date TEXT NOT NULL\\n",
    "    )\\n",
    "''')\\n",
    "\\n",
    "# 插入示例数据\\n",
    "employees = [\\n",
    "    (1, '张三', 'IT', 12000, '2023-01-15'),\\n",
    "    (2, '李四', 'IT', 10000, '2023-02-20'),\\n",
    "    (3, '王五', '销售', 8000, '2023-03-10'),\\n",
    "    (4, '赵六', '销售', 7000, '2023-04-05'),\\n",
    "    (5, '钱七', '市场', 9000, '2023-05-12'),\\n",
    "]\\n",
    "\\n",
    "cursor.executemany('INSERT OR REPLACE INTO employees VALUES (?, ?, ?, ?, ?)', employees)\\n",
    "conn.commit()\\n",
    "conn.close()\\n",
    "\\n",
    "print('✅ 示例数据库创建完成')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. 连接数据库并训练模型"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 连接数据库\\n",
    "vn.connect_to_sqlite('company.db')\\n",
    "\\n",
    "# 训练模型 - 添加数据库结构\\n",
    "vn.train(ddl='''\\n",
    "    CREATE TABLE employees (\\n",
    "        id INTEGER PRIMARY KEY,\\n",
    "        name TEXT NOT NULL,\\n",
    "        department TEXT NOT NULL,\\n",
    "        salary REAL NOT NULL,\\n",
    "        hire_date TEXT NOT NULL\\n",
    "    );\\n",
    "''')\\n",
    "\\n",
    "# 训练模型 - 添加业务文档\\n",
    "vn.train(documentation='''\\n",
    "    这是一个公司员工管理数据库。\\n",
    "    - employees 表包含员工的基本信息\\n",
    "    - 部门包括：IT、销售、市场、人力资源\\n",
    "    - 薪资以人民币为单位\\n",
    "    - hire_date 是员工入职日期\\n",
    "''')\\n",
    "\\n",
    "# 训练模型 - 添加示例查询\\n",
    "vn.train(sql='SELECT department, COUNT(*) as count FROM employees GROUP BY department')\\n",
    "vn.train(sql='SELECT name, salary FROM employees WHERE department = \\"IT\\" ORDER BY salary DESC')\\n",
    "\\n",
    "print('✅ 模型训练完成！')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. 测试中文查询"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 测试中文查询\\n",
    "question = '每个部门有多少员工？'\\n",
    "\\n",
    "print(f'❓ 问题: {question}')\\n",
    "\\n",
    "# 生成 SQL\\n",
    "sql = vn.generate_sql(question)\\n",
    "print(f'📝 生成的 SQL:\\\\n{sql}')\\n",
    "\\n",
    "# 执行查询\\n",
    "result = vn.run_sql(sql)\\n",
    "print(f'📊 结果:')\\n",
    "display(result)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 测试更复杂的中文查询\\n",
    "question2 = 'IT部门薪资最高的员工是谁？'\\n",
    "\\n",
    "print(f'❓ 问题: {question2}')\\n",
    "\\n",
    "sql2 = vn.generate_sql(question2)\\n",
    "print(f'📝 生成的 SQL:\\\\n{sql2}')\\n",
    "\\n",
    "result2 = vn.run_sql(sql2)\\n",
    "print(f'📊 结果:')\\n",
    "display(result2)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. 交互式查询"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 你可以在这里输入自己的问题\\n",
    "my_question = '平均薪资是多少？'  # 修改这里的问题\\n",
    "\\n",
    "print(f'❓ 你的问题: {my_question}')\\n",
    "\\n",
    "try:\\n",
    "    sql = vn.generate_sql(my_question)\\n",
    "    print(f'📝 生成的 SQL:\\\\n{sql}')\\n",
    "    \\n",
    "    result = vn.run_sql(sql)\\n",
    "    print(f'📊 结果:')\\n",
    "    display(result)\\n",
    "except Exception as e:\\n",
    "    print(f'❌ 查询失败: {e}')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 8. 性能测试"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import time\\n",
    "\\n",
    "# 测试查询速度\\n",
    "test_questions = [\\n",
    "    '显示所有员工',\\n",
    "    '每个部门的平均薪资',\\n",
    "    '2023年入职的员工有哪些',\\n",
    "    '薪资超过9000的员工'\\n",
    "]\\n",
    "\\n",
    "print('⚡ DeepSeek 性能测试')\\n",
    "print('=' * 30)\\n",
    "\\n",
    "for i, question in enumerate(test_questions, 1):\\n",
    "    print(f'\\\\n🧪 测试 {i}: {question}')\\n",
    "    \\n",
    "    start_time = time.time()\\n",
    "    try:\\n",
    "        sql = vn.generate_sql(question)\\n",
    "        end_time = time.time()\\n",
    "        \\n",
    "        print(f'⏱️ 生成时间: {end_time - start_time:.2f} 秒')\\n",
    "        print(f'📝 SQL: {sql}')\\n",
    "        \\n",
    "        result = vn.run_sql(sql)\\n",
    "        print(f'📊 结果行数: {len(result) if result is not None else 0}')\\n",
    "        \\n",
    "    except Exception as e:\\n",
    "        print(f'❌ 失败: {e}')"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.13.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}'''
    
    # 保存 Notebook
    with open('vanna_deepseek_demo.ipynb', 'w', encoding='utf-8') as f:
        f.write(notebook_content)
    
    print("✅ DeepSeek Jupyter Notebook 已创建: vanna_deepseek_demo.ipynb")

def main():
    """主函数"""
    print("📓 创建 Vanna + DeepSeek Notebook")
    print("=" * 40)
    
    create_deepseek_notebook()
    
    print("\n🚀 使用方法:")
    print("1. 获取 DeepSeek API 密钥: https://platform.deepseek.com/")
    print("2. 启动 Jupyter: jupyter notebook")
    print("3. 打开 vanna_deepseek_demo.ipynb")
    print("4. 修改第2个单元格中的 API 密钥")
    print("5. 按顺序执行所有单元格")
    
    choice = input("\n是否现在启动 Jupyter Notebook? (y/n): ").lower()
    if choice == 'y':
        import subprocess
        subprocess.run(['jupyter', 'notebook', 'vanna_deepseek_demo.ipynb'])

if __name__ == "__main__":
    main()
