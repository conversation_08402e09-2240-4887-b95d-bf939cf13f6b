"""
简单的测试服务器
===============

用于测试端口是否可以正常访问
"""

import http.server
import socketserver
import webbrowser
import threading
import time
import sys

def open_browser(port):
    """延迟打开浏览器"""
    time.sleep(2)
    try:
        webbrowser.open(f'http://localhost:{port}')
        print(f"🌐 浏览器应该已经打开 http://localhost:{port}")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print(f"请手动访问: http://localhost:{port}")

class TestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title><PERSON><PERSON> 端口测试</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                    .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    h1 { color: #2e7d32; }
                    .success { background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4caf50; }
                    .info { background: #e3f2fd; padding: 15px; border-radius: 5px; border-left: 4px solid #2196f3; }
                    .button { background: #1976d2; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
                    .button:hover { background: #1565c0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🎉 端口测试成功！</h1>
                    
                    <div class="success">
                        <strong>✅ 端口访问正常</strong><br>
                        如果你能看到这个页面，说明端口访问没有问题。
                    </div>
                    
                    <div class="info">
                        <h3>📋 下一步操作:</h3>
                        <ol>
                            <li>关闭这个测试服务器 (按 Ctrl+C)</li>
                            <li>运行 Vanna Web 应用</li>
                            <li>在浏览器中访问 Vanna 界面</li>
                        </ol>
                    </div>
                    
                    <h3>🚀 启动 Vanna 的命令:</h3>
                    <pre style="background: #f0f0f0; padding: 10px; border-radius: 5px;">
python web_app_demo.py
                    </pre>
                    
                    <p><strong>测试时间:</strong> {time}</p>
                </div>
            </body>
            </html>
            """.format(time=time.strftime('%Y-%m-%d %H:%M:%S'))
            
            self.wfile.write(html.encode('utf-8'))
        else:
            self.send_error(404, "页面未找到")

def main():
    """主函数"""
    # 获取端口参数
    port = 5000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ 无效的端口号")
            sys.exit(1)
    
    print(f"🧪 启动端口 {port} 测试服务器")
    print("=" * 40)
    
    try:
        # 启动服务器
        with socketserver.TCPServer(("", port), TestHandler) as httpd:
            print(f"✅ 测试服务器启动成功")
            print(f"🌐 访问地址: http://localhost:{port}")
            print("📱 正在尝试自动打开浏览器...")
            
            # 在后台线程中打开浏览器
            browser_thread = threading.Thread(target=open_browser, args=(port,), daemon=True)
            browser_thread.start()
            
            print("\n⏹️ 按 Ctrl+C 停止服务器")
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 10048:
            print(f"❌ 端口 {port} 已被占用")
            print("💡 尝试使用其他端口:")
            for test_port in [5001, 5002, 8000, 8080]:
                print(f"   python test_server.py {test_port}")
        elif e.errno == 10013:
            print(f"❌ 端口 {port} 权限被拒绝")
            print("💡 解决方案:")
            print("1. 以管理员身份运行")
            print("2. 使用大于 1024 的端口")
        else:
            print(f"❌ 启动失败: {e}")
    except KeyboardInterrupt:
        print("\n👋 测试服务器已停止")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

if __name__ == "__main__":
    main()
