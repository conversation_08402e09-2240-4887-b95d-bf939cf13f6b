#!/usr/bin/env python3
"""
简单的 Web 界面启动器
====================

直接启动，无需交互输入
"""

def main():
    print("🌐 启动 Vanna Web 界面")
    print("=" * 40)
    
    try:
        from vanna.openai import OpenAI_Chat
        from vanna.chromadb import ChromaDB_VectorStore
        from vanna.flask import VannaFlaskApp
        from openai import OpenAI
        import webbrowser
        import time
        import threading
        
        # 配置信息
        DEEPSEEK_KEY = "***********************************"
        MYSQL_PASSWORD = "22223333"
        
        print("🔧 初始化 Vanna + DeepSeek...")
        
        # 创建客户端
        client = OpenAI(
            api_key=DEEPSEEK_KEY,
            base_url="https://api.deepseek.com"
        )
        
        # 创建 Vanna 类
        class VannaDeepSeek(ChromaDB_VectorStore, OpenAI_Chat):
            def __init__(self, config=None):
                ChromaDB_VectorStore.__init__(self, config=config)
                OpenAI_Chat.__init__(self, client=client, config=config)
        
        # 初始化
        vn = VannaDeepSeek(config={'model': 'deepseek-chat'})
        
        # 🔧 关键设置：允许 LLM 查看数据
        vn.allow_llm_to_see_data = True
        
        print("✅ Vanna 初始化完成")
        print(f"🔒 权限设置: allow_llm_to_see_data = {vn.allow_llm_to_see_data}")
        
        # 连接数据库
        print("🔗 连接到 MySQL...")
        vn.connect_to_mysql(
            host='***********',
            dbname='project',
            user='root',
            password=MYSQL_PASSWORD,
            port=3306
        )
        print("✅ MySQL 连接成功")
        
        # 快速训练
        print("🎓 快速训练...")
        vn.train(documentation="sys_project_info是工程表，del=0表示有效记录")
        vn.train(question="工程总数", sql="SELECT COUNT(*) FROM sys_project_info WHERE del = 0")
        print("✅ 训练完成")
        
        # 启动 Web 界面
        port = 5002  # 使用新端口避免冲突
        
        print(f"🌐 启动 Web 界面在端口 {port}")
        print(f"📱 访问地址: http://localhost:{port}")
        
        # 自动打开浏览器
        def open_browser():
            time.sleep(3)
            webbrowser.open(f'http://localhost:{port}')
            print("🌐 浏览器已打开")
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        print("\n🎯 权限问题已修复！")
        print("✅ 数据汇总功能已启用")
        print("✅ 中文查询支持已启用")
        
        print("\n💡 测试查询:")
        print("   - 库中一共多少工程")
        print("   - 需要审计的工程数量")
        print("   - 2020年以后的工程")
        
        print("\n⏹️ 按 Ctrl+C 停止")
        
        # 启动应用
        app = VannaFlaskApp(vn)
        app.run(host='127.0.0.1', port=port, debug=False)
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
