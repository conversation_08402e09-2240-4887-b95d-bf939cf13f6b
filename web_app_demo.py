"""
Vanna Web 应用演示
=================

解决端口访问问题的 Web 界面启动脚本
"""

import os
import sys
import socket

def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def find_available_port(start_port=5000, max_attempts=10):
    """找到可用的端口"""
    for port in range(start_port, start_port + max_attempts):
        if check_port_available(port):
            return port
    return None

def start_web_app_with_sample_data():
    """使用示例数据启动 Web 应用"""
    try:
        import vanna
        from vanna.remote import VannaDefault
        from vanna.flask import VannaFlaskApp
        
        print("🌐 启动 Vanna Web 应用")
        print("=" * 40)
        
        # 获取 API 密钥
        print("需要 Vanna API 密钥来使用远程服务")
        email = input("请输入你的邮箱地址: ")
        
        try:
            api_key = vanna.get_api_key(email)
            print("✅ API 密钥获取成功")
        except Exception as e:
            print(f"❌ 获取 API 密钥失败: {e}")
            return
        
        # 初始化 Vanna
        print("🔧 初始化 Vanna...")
        vn = VannaDefault(model='chinook', api_key=api_key)
        
        # 连接到示例数据库
        print("🔗 连接到示例数据库...")
        vn.connect_to_sqlite('https://vanna.ai/Chinook.sqlite')
        print("✅ 数据库连接成功！")
        
        # 找到可用端口
        port = find_available_port(5000)
        if port is None:
            print("❌ 无法找到可用端口")
            return
        
        print(f"🚀 启动 Web 应用在端口 {port}...")
        print(f"📱 请在浏览器中访问: http://localhost:{port}")
        print("按 Ctrl+C 停止服务器")
        
        # 启动 Flask 应用
        app = VannaFlaskApp(vn)
        app.run(host='0.0.0.0', port=port, debug=True)
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装 vanna: pip install vanna")
    except KeyboardInterrupt:
        print("\n👋 Web 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def start_local_web_app():
    """使用本地配置启动 Web 应用"""
    try:
        from vanna.openai.openai_chat import OpenAI_Chat
        from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
        from vanna.flask import VannaFlaskApp
        import sqlite3
        
        print("🏠 启动本地 Vanna Web 应用")
        print("=" * 40)
        
        # 获取 OpenAI API 密钥
        api_key = input("请输入你的 OpenAI API 密钥: ").strip()
        if not api_key:
            print("❌ 需要 OpenAI API 密钥")
            return
        
        # 创建 Vanna 类
        class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
            def __init__(self, config=None):
                ChromaDB_VectorStore.__init__(self, config=config)
                OpenAI_Chat.__init__(self, config=config)
        
        # 初始化
        print("🔧 初始化 Vanna...")
        vn = MyVanna(config={'api_key': api_key, 'model': 'gpt-3.5-turbo'})
        
        # 创建示例数据库
        print("📊 创建示例数据库...")
        conn = sqlite3.connect('demo.db')
        cursor = conn.cursor()
        
        # 创建表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY,
                name TEXT,
                category TEXT,
                price REAL,
                stock INTEGER
            )
        """)
        
        # 插入示例数据
        sample_products = [
            (1, 'iPhone 15', '电子产品', 999.99, 50),
            (2, 'MacBook Pro', '电子产品', 1999.99, 30),
            (3, 'Nike 运动鞋', '服装', 129.99, 100),
            (4, '咖啡机', '家电', 299.99, 25),
            (5, '办公椅', '家具', 199.99, 40),
        ]
        
        cursor.executemany(
            "INSERT OR REPLACE INTO products VALUES (?, ?, ?, ?, ?)",
            sample_products
        )
        conn.commit()
        conn.close()
        
        # 连接数据库
        vn.connect_to_sqlite('demo.db')
        
        # 训练模型
        print("🎓 训练模型...")
        vn.train(ddl="""
            CREATE TABLE products (
                id INTEGER PRIMARY KEY,
                name TEXT,
                category TEXT,
                price REAL,
                stock INTEGER
            );
        """)
        
        vn.train(documentation="产品表包含商店的所有产品信息，包括名称、类别、价格和库存。")
        vn.train(sql="SELECT category, COUNT(*) as count FROM products GROUP BY category")
        
        print("✅ 训练完成！")
        
        # 找到可用端口
        port = find_available_port(5000)
        if port is None:
            print("❌ 无法找到可用端口")
            return
        
        print(f"🚀 启动 Web 应用在端口 {port}...")
        print(f"📱 请在浏览器中访问: http://localhost:{port}")
        print("按 Ctrl+C 停止服务器")
        
        # 启动应用
        app = VannaFlaskApp(vn)
        app.run(host='127.0.0.1', port=port, debug=False)
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("可能需要安装额外依赖:")
        print("pip install 'vanna[chromadb,openai]'")
    except KeyboardInterrupt:
        print("\n👋 Web 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def troubleshoot_port_issues():
    """端口问题排查"""
    print("🔧 端口问题排查")
    print("=" * 30)
    
    # 检查常用端口
    ports_to_check = [5000, 5001, 5002, 8000, 8080]
    
    print("检查端口可用性:")
    for port in ports_to_check:
        if check_port_available(port):
            print(f"✅ 端口 {port} 可用")
        else:
            print(f"❌ 端口 {port} 被占用")
    
    print("\n💡 解决方案:")
    print("1. 使用不同的端口")
    print("2. 检查防火墙设置")
    print("3. 确保没有其他应用占用端口")
    print("4. 尝试以管理员身份运行")

def main():
    """主函数"""
    print("🌐 Vanna Web 应用启动器")
    print("=" * 40)
    
    print("\n选择启动方式:")
    print("1. 使用示例数据启动 (推荐)")
    print("2. 使用本地配置启动 (需要 OpenAI API)")
    print("3. 端口问题排查")
    print("4. 退出")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        start_web_app_with_sample_data()
    elif choice == "2":
        start_local_web_app()
    elif choice == "3":
        troubleshoot_port_issues()
    elif choice == "4":
        print("👋 再见！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
