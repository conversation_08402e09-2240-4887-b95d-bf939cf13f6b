"""
Vanna AI 演示脚本
================

这个脚本展示了如何使用 Vanna AI 进行自然语言到 SQL 的转换。
"""

import os
import sys

def demo_with_sample_data():
    """使用示例数据的演示"""
    try:
        import vanna
        from vanna.remote import VannaDefault
        
        print("🚀 Vanna AI 演示 - 使用示例数据")
        print("=" * 50)
        
        # 获取 API 密钥
        print("首次使用需要注册获取免费 API 密钥")
        email = input("请输入你的邮箱地址: ")
        
        try:
            api_key = vanna.get_api_key(email)
            print("✅ API 密钥获取成功")
        except Exception as e:
            print(f"❌ 获取 API 密钥失败: {e}")
            return
        
        # 初始化 Vanna（使用预训练的 Chinook 数据库模型）
        print("🔧 初始化 Vanna...")
        vn = VannaDefault(model='chinook', api_key=api_key)
        
        # 连接到示例数据库
        print("🔗 连接到示例数据库...")
        vn.connect_to_sqlite('https://vanna.ai/Chinook.sqlite')
        print("✅ 数据库连接成功！")
        
        # 显示数据库信息
        print("\n📊 数据库信息:")
        print("这是一个音乐商店数据库，包含以下表:")
        print("- albums (专辑)")
        print("- artists (艺术家)")
        print("- customers (客户)")
        print("- invoices (发票)")
        print("- tracks (曲目)")
        
        # 预定义的示例问题
        sample_questions = [
            "What are the top 10 albums by sales?",
            "Show me all customers from USA",
            "What is the total revenue by year?",
            "Which artist has the most albums?",
            "What are the most popular music genres?"
        ]
        
        print("\n💡 示例问题:")
        for i, q in enumerate(sample_questions, 1):
            print(f"{i}. {q}")
        
        # 交互式查询
        print("\n🤖 现在你可以开始提问了！")
        print("输入 'quit' 退出，输入 'examples' 查看示例问题")
        
        while True:
            question = input("\n❓ 你的问题: ").strip()
            
            if question.lower() == 'quit':
                print("👋 再见！")
                break
            elif question.lower() == 'examples':
                print("\n💡 示例问题:")
                for i, q in enumerate(sample_questions, 1):
                    print(f"{i}. {q}")
                continue
            elif not question:
                continue
            
            try:
                print("🔄 正在生成 SQL 查询...")
                
                # 生成 SQL
                sql = vn.generate_sql(question)
                print(f"\n📝 生成的 SQL:")
                print(sql)
                
                # 执行查询并获取结果
                print("\n⚡ 执行查询...")
                result = vn.run_sql(sql)
                
                if result is not None and not result.empty:
                    print(f"\n📊 查询结果 ({len(result)} 行):")
                    print(result.to_string(index=False))
                else:
                    print("📭 查询无结果")
                    
            except Exception as e:
                print(f"❌ 查询出错: {e}")
                print("💡 提示: 尝试重新表述你的问题")
                
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装 vanna: pip install vanna")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


def demo_local_setup():
    """本地设置演示（需要 OpenAI API 密钥）"""
    print("🏠 本地设置演示")
    print("=" * 30)
    print("注意: 这需要 OpenAI API 密钥")
    
    api_key = input("请输入你的 OpenAI API 密钥 (或按 Enter 跳过): ").strip()
    
    if not api_key:
        print("⏭️ 跳过本地设置演示")
        return
    
    try:
        from vanna.openai.openai_chat import OpenAI_Chat
        from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
        
        class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
            def __init__(self, config=None):
                ChromaDB_VectorStore.__init__(self, config=config)
                OpenAI_Chat.__init__(self, config=config)
        
        # 初始化
        vn = MyVanna(config={'api_key': api_key, 'model': 'gpt-3.5-turbo'})
        
        # 创建示例 SQLite 数据库
        import sqlite3
        conn = sqlite3.connect('demo.db')
        cursor = conn.cursor()
        
        # 创建示例表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY,
                name TEXT,
                department TEXT,
                salary REAL,
                hire_date TEXT
            )
        """)
        
        # 插入示例数据
        sample_data = [
            (1, '张三', 'IT', 8000, '2023-01-15'),
            (2, '李四', '销售', 6000, '2023-02-20'),
            (3, '王五', 'IT', 9000, '2023-03-10'),
            (4, '赵六', '市场', 7000, '2023-04-05'),
        ]
        
        cursor.executemany(
            "INSERT OR REPLACE INTO employees VALUES (?, ?, ?, ?, ?)",
            sample_data
        )
        conn.commit()
        conn.close()
        
        # 连接到数据库
        vn.connect_to_sqlite('demo.db')
        
        # 训练模型
        print("🎓 训练模型...")
        vn.train(ddl="""
            CREATE TABLE employees (
                id INTEGER PRIMARY KEY,
                name TEXT,
                department TEXT,
                salary REAL,
                hire_date TEXT
            );
        """)
        
        vn.train(documentation="员工表包含公司员工信息，包括姓名、部门、薪资和入职日期。")
        
        print("✅ 训练完成！")
        print("现在你可以查询员工数据库了")
        
        # 示例查询
        questions = [
            "显示所有员工",
            "IT部门有多少员工？",
            "薪资最高的员工是谁？"
        ]
        
        for question in questions:
            print(f"\n❓ 问题: {question}")
            try:
                sql = vn.generate_sql(question)
                print(f"📝 SQL: {sql}")
                result = vn.run_sql(sql)
                print(f"📊 结果:\n{result}")
            except Exception as e:
                print(f"❌ 错误: {e}")
                
    except Exception as e:
        print(f"❌ 本地设置失败: {e}")


def main():
    """主函数"""
    print("🎯 Vanna AI 演示程序")
    print("=" * 40)
    
    print("\n选择演示模式:")
    print("1. 使用示例数据 (推荐开始)")
    print("2. 本地设置演示 (需要 OpenAI API)")
    print("3. 查看帮助信息")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        demo_with_sample_data()
    elif choice == "2":
        demo_local_setup()
    elif choice == "3":
        print("\n📚 帮助信息:")
        print("- 运行 'python simple_start.py' 开始最简单的示例")
        print("- 查看 'README.md' 了解详细说明")
        print("- 查看 'vanna_quickstart.py' 了解更多示例")
        print("- 访问 https://vanna.ai/docs/ 查看官方文档")
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main()
