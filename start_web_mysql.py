"""
启动 MySQL + DeepSeek Web 界面
=============================

专门用于启动 Web 界面的脚本
"""

import socket
import webbrowser
import threading
import time

def check_dependencies():
    """检查所需依赖"""
    dependencies = {
        'vanna': 'vanna',
        'openai': 'openai', 
        'pymysql': 'pymysql',
        'flask': 'flask'
    }
    
    missing = []
    
    for name, package in dependencies.items():
        try:
            __import__(package)
            print(f"✅ {name} 已安装")
        except ImportError:
            missing.append(package)
            print(f"❌ {name} 未安装")
    
    if missing:
        print(f"\n📦 需要安装缺失的依赖:")
        print(f"pip install {' '.join(missing)}")
        return False
    
    return True

def find_available_port(start_port=5000):
    """找到可用端口"""
    for port in range(start_port, start_port + 20):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return port
        except OSError:
            continue
    return None

def setup_vanna_deepseek():
    """设置 Vanna + DeepSeek（中文版）"""

    # 获取 DeepSeek API 密钥
    api_key = input("请输入你的 DeepSeek API 密钥: ").strip()
    if not api_key:
        print("❌ 需要 DeepSeek API 密钥")
        return None

    try:
        # 使用中文配置
        from chinese_vanna_config import setup_chinese_vanna_deepseek, add_chinese_training_data

        print("🔧 使用中文专用配置...")
        vn = setup_chinese_vanna_deepseek(api_key)

        # 添加中文训练数据
        add_chinese_training_data(vn)

        print("✅ Vanna + DeepSeek 初始化成功！(中文专用版)")
        return vn

    except ImportError:
        print("⚠️ 中文配置模块未找到，使用基础配置")

        # 备用基础配置
        from vanna.openai import OpenAI_Chat
        from vanna.chromadb import ChromaDB_VectorStore
        from openai import OpenAI

        # 创建 DeepSeek 客户端
        deepseek_client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )

        # 创建自定义 Vanna 类
        class VannaDeepSeek(ChromaDB_VectorStore, OpenAI_Chat):
            def __init__(self, config=None):
                ChromaDB_VectorStore.__init__(self, config=config)
                OpenAI_Chat.__init__(self, client=deepseek_client, config=config)

        # 初始化 Vanna
        vn = VannaDeepSeek(config={
            'model': 'deepseek-chat',
            'temperature': 0.1,
            'max_tokens': 2000,
        })

        print("✅ Vanna + DeepSeek 初始化成功！(基础版)")
        return vn

def connect_to_mysql(vn):
    """连接到 MySQL"""
    print("🔗 MySQL 连接配置")
    print("=" * 20)
    
    # 使用之前测试成功的连接信息
    print("💡 使用之前测试成功的连接信息:")
    use_saved = input("使用 ***********:3306/project? (y/n): ").lower()
    
    if use_saved == 'y':
        host = "***********"
        port = 3306
        user = "root"
        password = input("请输入密码: ").strip()
        database = "project"
    else:
        host = input("主机地址: ").strip()
        port = int(input("端口 (默认: 3306): ").strip() or "3306")
        user = input("用户名: ").strip()
        password = input("密码: ").strip()
        database = input("数据库名: ").strip()
    
    try:
        print("🔄 连接到 MySQL...")
        vn.connect_to_mysql(
            host=host,
            dbname=database,
            user=user,
            password=password,
            port=port
        )
        print("✅ MySQL 连接成功！")
        return True
    except Exception as e:
        print(f"❌ MySQL 连接失败: {e}")
        return False

def auto_train_mysql(vn):
    """自动训练 MySQL 数据"""
    print("🎓 自动训练数据库结构...")
    
    try:
        # 获取表信息
        tables_result = vn.run_sql("SHOW TABLES")
        if tables_result is not None and not tables_result.empty:
            table_names = tables_result.iloc[:, 0].tolist()
            print(f"📊 找到 {len(table_names)} 个表")
            
            # 为每个表生成 DDL
            for table_name in table_names:
                try:
                    create_result = vn.run_sql(f"SHOW CREATE TABLE `{table_name}`")
                    if create_result is not None and not create_result.empty:
                        create_sql = create_result.iloc[0, 1]
                        vn.train(ddl=create_sql)
                        print(f"   ✅ {table_name}")
                except Exception as e:
                    print(f"   ❌ {table_name}: {e}")
        
        # 加载专用训练数据
        try:
            from project_training_data import train_project_database, train_business_documentation
            print("📚 加载项目管理专用训练数据...")
            train_business_documentation(vn)
            train_project_database(vn)
            print("✅ 专用训练数据加载完成！")
        except ImportError:
            print("⚠️ 未找到专用训练数据，使用基础训练")
        
        print("✅ 自动训练完成！")
        return True
        
    except Exception as e:
        print(f"❌ 自动训练失败: {e}")
        return False

def start_web_app(vn):
    """启动 Web 应用"""
    try:
        from vanna.flask import VannaFlaskApp
        
        print("🌐 启动 Web 界面...")
        print("=" * 30)
        
        # 找到可用端口
        port = find_available_port()
        if port is None:
            print("❌ 无法找到可用端口")
            return
        
        print(f"🚀 Web 服务器启动在端口 {port}")
        print(f"📱 访问地址: http://localhost:{port}")
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🌐 浏览器已自动打开")
            except Exception as e:
                print(f"⚠️ 无法自动打开浏览器: {e}")
                print(f"请手动访问: http://localhost:{port}")
        
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        print("\n🎯 Web 界面功能:")
        print("   - 自然语言查询数据库")
        print("   - 查看生成的 SQL 代码")
        print("   - 结果表格显示")
        print("   - 数据可视化图表")
        print("   - 查询历史记录")
        
        print("\n💡 使用提示:")
        print("   - 可以用中文提问")
        print("   - 例如: '库中一共多少工程'")
        print("   - 例如: '2020年以后已审计的工程'")
        
        print("\n⏹️ 按 Ctrl+C 停止服务器")
        
        # 启动 Flask 应用
        app = VannaFlaskApp(vn)
        app.run(
            host='127.0.0.1',
            port=port,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Web 服务器已停止")
    except Exception as e:
        print(f"❌ Web 界面启动失败: {e}")

def main():
    """主函数"""
    print("🌐 MySQL + DeepSeek Web 界面启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 设置 Vanna + DeepSeek
    vn = setup_vanna_deepseek()
    if vn is None:
        return
    
    # 连接 MySQL
    if not connect_to_mysql(vn):
        return
    
    # 自动训练
    auto_train = input("\n是否自动训练数据库结构? (y/n): ").lower()
    if auto_train == 'y':
        if not auto_train_mysql(vn):
            print("⚠️ 训练失败，但仍可继续使用")
    
    # 启动 Web 界面
    print("\n🚀 准备启动 Web 界面...")
    start_web_app(vn)

if __name__ == "__main__":
    main()
