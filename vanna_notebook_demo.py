"""
Vanna Jupyter Notebook 演示
==========================

避免端口问题，直接在 Jupyter 中使用 Vanna
"""

def create_jupyter_notebook():
    """创建 Jupyter Notebook 演示"""
    
    notebook_content = '''
{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Vanna AI 演示\\n",
    "\\n",
    "这个 Notebook 展示如何使用 Vanna AI 进行自然语言到 SQL 的转换。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. 安装和导入"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 安装 Vanna (如果还没安装)\\n",
    "# !pip install vanna\\n",
    "\\n",
    "import vanna\\n",
    "from vanna.remote import VannaDefault\\n",
    "import pandas as pd"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. 初始化 Vanna"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 获取 API 密钥 (第一次使用时需要输入邮箱)\\n",
    "email = '<EMAIL>'  # 替换为你的邮箱\\n",
    "api_key = vanna.get_api_key(email)\\n",
    "\\n",
    "# 初始化 Vanna (使用预训练的 Chinook 数据库模型)\\n",
    "vn = VannaDefault(model='chinook', api_key=api_key)\\n",
    "\\n",
    "# 连接到示例数据库\\n",
    "vn.connect_to_sqlite('https://vanna.ai/Chinook.sqlite')\\n",
    "\\n",
    "print('✅ Vanna 初始化完成！')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. 了解数据库结构"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 查看数据库中的表\\n",
    "tables = vn.run_sql(\\"SELECT name FROM sqlite_master WHERE type='table'\\")\\n",
    "print('📊 数据库中的表:')\\n",
    "print(tables)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. 开始查询"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 示例查询 1: 销量最高的专辑\\n",
    "question1 = \\"What are the top 10 albums by sales?\\"\\n",
    "\\n",
    "print(f'❓ 问题: {question1}')\\n",
    "sql1 = vn.generate_sql(question1)\\n",
    "print(f'📝 生成的 SQL:\\\\n{sql1}')\\n",
    "\\n",
    "result1 = vn.run_sql(sql1)\\n",
    "print(f'📊 结果:')\\n",
    "display(result1)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 示例查询 2: 美国客户\\n",
    "question2 = \\"Show me all customers from USA\\"\\n",
    "\\n",
    "print(f'❓ 问题: {question2}')\\n",
    "sql2 = vn.generate_sql(question2)\\n",
    "print(f'📝 生成的 SQL:\\\\n{sql2}')\\n",
    "\\n",
    "result2 = vn.run_sql(sql2)\\n",
    "print(f'📊 结果:')\\n",
    "display(result2.head(10))  # 只显示前10行"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 示例查询 3: 年度收入\\n",
    "question3 = \\"What is the total revenue by year?\\"\\n",
    "\\n",
    "print(f'❓ 问题: {question3}')\\n",
    "sql3 = vn.generate_sql(question3)\\n",
    "print(f'📝 生成的 SQL:\\\\n{sql3}')\\n",
    "\\n",
    "result3 = vn.run_sql(sql3)\\n",
    "print(f'📊 结果:')\\n",
    "display(result3)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. 可视化结果"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 获取可视化图表\\n",
    "import plotly.express as px\\n",
    "\\n",
    "# 使用之前的年度收入数据创建图表\\n",
    "if 'result3' in locals() and not result3.empty:\\n",
    "    fig = px.bar(result3, x=result3.columns[0], y=result3.columns[1], \\n",
    "                 title='年度收入趋势')\\n",
    "    fig.show()\\n",
    "else:\\n",
    "    print('请先运行上面的查询获取数据')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. 自定义查询"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 你可以在这里输入自己的问题\\n",
    "my_question = \\"Which artist has the most albums?\\"  # 修改这里的问题\\n",
    "\\n",
    "print(f'❓ 你的问题: {my_question}')\\n",
    "\\n",
    "try:\\n",
    "    sql = vn.generate_sql(my_question)\\n",
    "    print(f'📝 生成的 SQL:\\\\n{sql}')\\n",
    "    \\n",
    "    result = vn.run_sql(sql)\\n",
    "    print(f'📊 结果:')\\n",
    "    display(result)\\n",
    "except Exception as e:\\n",
    "    print(f'❌ 查询失败: {e}')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. 使用 Vanna 的内置可视化"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 使用 Vanna 的 ask 方法，它会自动生成可视化\\n",
    "question = \\"What are the most popular music genres?\\"\\n",
    "\\n",
    "print(f'❓ 问题: {question}')\\n",
    "\\n",
    "# ask 方法会返回 SQL、结果和可视化\\n",
    "response = vn.ask(question)\\n",
    "print('✅ 查询完成！查看上面的输出获取结果和图表。')"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
'''
    
    # 保存 Notebook
    with open('vanna_demo.ipynb', 'w', encoding='utf-8') as f:
        f.write(notebook_content)
    
    print("✅ Jupyter Notebook 已创建: vanna_demo.ipynb")
    print("🚀 启动方法:")
    print("   jupyter notebook vanna_demo.ipynb")

def install_jupyter():
    """安装 Jupyter"""
    import subprocess
    import sys
    
    try:
        import jupyter
        print("✅ Jupyter 已安装")
        return True
    except ImportError:
        print("📦 安装 Jupyter...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'jupyter'])
            print("✅ Jupyter 安装成功")
            return True
        except Exception as e:
            print(f"❌ Jupyter 安装失败: {e}")
            return False

def main():
    """主函数"""
    print("📓 Vanna Jupyter Notebook 演示")
    print("=" * 40)
    
    print("这是避免端口问题的最佳方案！")
    print("Jupyter Notebook 提供了更好的交互体验。")
    
    # 检查并安装 Jupyter
    if not install_jupyter():
        return
    
    # 创建 Notebook
    create_jupyter_notebook()
    
    print("\n🎯 使用步骤:")
    print("1. 运行: jupyter notebook")
    print("2. 在浏览器中打开 vanna_demo.ipynb")
    print("3. 按顺序执行每个单元格")
    print("4. 修改问题并尝试自己的查询")
    
    choice = input("\n是否现在启动 Jupyter Notebook? (y/n): ").lower()
    if choice == 'y':
        import subprocess
        subprocess.run(['jupyter', 'notebook', 'vanna_demo.ipynb'])

if __name__ == "__main__":
    main()
