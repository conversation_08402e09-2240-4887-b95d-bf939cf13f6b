"""
数据库 DDL 生成工具
==================

从不同类型的数据库生成 DDL 文件，用于 Vanna 训练
"""

import os
import sys

def generate_mysql_ddl(host, user, password, database, output_file="mysql_ddl.sql"):
    """生成 MySQL DDL"""
    try:
        import pymysql
        
        print(f"🔗 连接到 MySQL 数据库: {database}")
        
        connection = pymysql.connect(
            host=host,
            user=user,
            password=password,
            database=database
        )
        
        cursor = connection.cursor()
        
        # 获取所有表名
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        
        ddl_content = f"-- MySQL DDL for database: {database}\n"
        ddl_content += f"-- Generated on: {__import__('datetime').datetime.now()}\n\n"
        
        for table in tables:
            print(f"📋 处理表: {table}")
            
            # 获取建表语句
            cursor.execute(f"SHOW CREATE TABLE `{table}`")
            create_table = cursor.fetchone()[1]
            
            ddl_content += f"-- Table: {table}\n"
            ddl_content += create_table + ";\n\n"
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(ddl_content)
        
        connection.close()
        print(f"✅ MySQL DDL 已保存到: {output_file}")
        return output_file
        
    except ImportError:
        print("❌ 需要安装 pymysql: pip install pymysql")
        return None
    except Exception as e:
        print(f"❌ MySQL DDL 生成失败: {e}")
        return None

def generate_postgresql_ddl(host, user, password, database, port=5432, output_file="postgresql_ddl.sql"):
    """生成 PostgreSQL DDL"""
    try:
        import psycopg2
        
        print(f"🔗 连接到 PostgreSQL 数据库: {database}")
        
        connection = psycopg2.connect(
            host=host,
            user=user,
            password=password,
            database=database,
            port=port
        )
        
        cursor = connection.cursor()
        
        # 获取所有表名
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
        """)
        
        tables = [table[0] for table in cursor.fetchall()]
        
        ddl_content = f"-- PostgreSQL DDL for database: {database}\n"
        ddl_content += f"-- Generated on: {__import__('datetime').datetime.now()}\n\n"
        
        for table in tables:
            print(f"📋 处理表: {table}")
            
            # 获取表结构
            cursor.execute(f"""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_name = '{table}'
                ORDER BY ordinal_position
            """)
            
            columns = cursor.fetchall()
            
            ddl_content += f"-- Table: {table}\n"
            ddl_content += f"CREATE TABLE {table} (\n"
            
            column_definitions = []
            for col_name, data_type, is_nullable, col_default in columns:
                col_def = f"    {col_name} {data_type}"
                
                if is_nullable == 'NO':
                    col_def += " NOT NULL"
                
                if col_default:
                    col_def += f" DEFAULT {col_default}"
                
                column_definitions.append(col_def)
            
            ddl_content += ",\n".join(column_definitions)
            ddl_content += "\n);\n\n"
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(ddl_content)
        
        connection.close()
        print(f"✅ PostgreSQL DDL 已保存到: {output_file}")
        return output_file
        
    except ImportError:
        print("❌ 需要安装 psycopg2: pip install psycopg2-binary")
        return None
    except Exception as e:
        print(f"❌ PostgreSQL DDL 生成失败: {e}")
        return None

def generate_sqlite_ddl(db_path, output_file="sqlite_ddl.sql"):
    """生成 SQLite DDL"""
    try:
        import sqlite3
        
        print(f"🔗 连接到 SQLite 数据库: {db_path}")
        
        connection = sqlite3.connect(db_path)
        cursor = connection.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        
        ddl_content = f"-- SQLite DDL for database: {db_path}\n"
        ddl_content += f"-- Generated on: {__import__('datetime').datetime.now()}\n\n"
        
        for table in tables:
            print(f"📋 处理表: {table}")
            
            # 获取建表语句
            cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table}'")
            create_table = cursor.fetchone()[0]
            
            ddl_content += f"-- Table: {table}\n"
            ddl_content += create_table + ";\n\n"
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(ddl_content)
        
        connection.close()
        print(f"✅ SQLite DDL 已保存到: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ SQLite DDL 生成失败: {e}")
        return None

def generate_sqlserver_ddl(server, user, password, database, output_file="sqlserver_ddl.sql"):
    """生成 SQL Server DDL"""
    try:
        import pyodbc
        
        print(f"🔗 连接到 SQL Server 数据库: {database}")
        
        connection_string = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={user};PWD={password}"
        connection = pyodbc.connect(connection_string)
        cursor = connection.cursor()
        
        # 获取所有表名
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
        """)
        
        tables = [table[0] for table in cursor.fetchall()]
        
        ddl_content = f"-- SQL Server DDL for database: {database}\n"
        ddl_content += f"-- Generated on: {__import__('datetime').datetime.now()}\n\n"
        
        for table in tables:
            print(f"📋 处理表: {table}")
            
            # 获取表结构
            cursor.execute(f"""
                SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = '{table}'
                ORDER BY ORDINAL_POSITION
            """)
            
            columns = cursor.fetchall()
            
            ddl_content += f"-- Table: {table}\n"
            ddl_content += f"CREATE TABLE [{table}] (\n"
            
            column_definitions = []
            for col_name, data_type, is_nullable, col_default in columns:
                col_def = f"    [{col_name}] {data_type}"
                
                if is_nullable == 'NO':
                    col_def += " NOT NULL"
                
                if col_default:
                    col_def += f" DEFAULT {col_default}"
                
                column_definitions.append(col_def)
            
            ddl_content += ",\n".join(column_definitions)
            ddl_content += "\n);\n\n"
        
        # 保存到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(ddl_content)
        
        connection.close()
        print(f"✅ SQL Server DDL 已保存到: {output_file}")
        return output_file
        
    except ImportError:
        print("❌ 需要安装 pyodbc: pip install pyodbc")
        return None
    except Exception as e:
        print(f"❌ SQL Server DDL 生成失败: {e}")
        return None

def auto_generate_ddl_for_vanna(ddl_file):
    """自动将 DDL 文件用于 Vanna 训练"""
    if not os.path.exists(ddl_file):
        print(f"❌ DDL 文件不存在: {ddl_file}")
        return
    
    print(f"📚 为 Vanna 准备 DDL 训练数据...")
    
    with open(ddl_file, 'r', encoding='utf-8') as f:
        ddl_content = f.read()
    
    # 生成 Vanna 训练代码
    vanna_code = f'''
# 使用生成的 DDL 训练 Vanna
# 文件: {ddl_file}

# 方法 1: 直接使用整个 DDL 文件
with open('{ddl_file}', 'r', encoding='utf-8') as f:
    ddl_content = f.read()
    vn.train(ddl=ddl_content)

# 方法 2: 按表分别训练（推荐）
ddl_statements = []
current_statement = ""

with open('{ddl_file}', 'r', encoding='utf-8') as f:
    for line in f:
        if line.strip() and not line.startswith('--'):
            current_statement += line
            if line.strip().endswith(';'):
                ddl_statements.append(current_statement.strip())
                current_statement = ""

for ddl in ddl_statements:
    if ddl.upper().startswith('CREATE TABLE'):
        vn.train(ddl=ddl)
        print(f"✅ 训练完成: {{ddl[:50]}}...")
'''
    
    # 保存 Vanna 训练代码
    vanna_file = ddl_file.replace('.sql', '_vanna_training.py')
    with open(vanna_file, 'w', encoding='utf-8') as f:
        f.write(vanna_code)
    
    print(f"✅ Vanna 训练代码已保存到: {vanna_file}")

def main():
    """主函数"""
    print("🗄️ 数据库 DDL 生成工具")
    print("=" * 40)
    
    print("支持的数据库类型:")
    print("1. MySQL")
    print("2. PostgreSQL") 
    print("3. SQLite")
    print("4. SQL Server")
    
    choice = input("\n请选择数据库类型 (1-4): ").strip()
    
    if choice == "1":
        print("\n📋 MySQL 连接信息:")
        host = input("主机地址 (默认: localhost): ").strip() or "localhost"
        user = input("用户名: ").strip()
        password = input("密码: ").strip()
        database = input("数据库名: ").strip()
        
        ddl_file = generate_mysql_ddl(host, user, password, database)
        
    elif choice == "2":
        print("\n📋 PostgreSQL 连接信息:")
        host = input("主机地址 (默认: localhost): ").strip() or "localhost"
        user = input("用户名: ").strip()
        password = input("密码: ").strip()
        database = input("数据库名: ").strip()
        port = input("端口 (默认: 5432): ").strip() or "5432"
        
        ddl_file = generate_postgresql_ddl(host, user, password, database, int(port))
        
    elif choice == "3":
        print("\n📋 SQLite 数据库:")
        db_path = input("数据库文件路径: ").strip()
        
        ddl_file = generate_sqlite_ddl(db_path)
        
    elif choice == "4":
        print("\n📋 SQL Server 连接信息:")
        server = input("服务器地址: ").strip()
        user = input("用户名: ").strip()
        password = input("密码: ").strip()
        database = input("数据库名: ").strip()
        
        ddl_file = generate_sqlserver_ddl(server, user, password, database)
        
    else:
        print("❌ 无效选择")
        return
    
    if ddl_file:
        print(f"\n🎯 DDL 文件生成成功: {ddl_file}")
        
        generate_vanna = input("\n是否生成 Vanna 训练代码? (y/n): ").lower()
        if generate_vanna == 'y':
            auto_generate_ddl_for_vanna(ddl_file)
        
        print("\n📚 使用方法:")
        print(f"1. 查看生成的 DDL: {ddl_file}")
        print("2. 在 Vanna 中使用:")
        print(f"   vn.train(ddl=open('{ddl_file}').read())")

if __name__ == "__main__":
    main()
