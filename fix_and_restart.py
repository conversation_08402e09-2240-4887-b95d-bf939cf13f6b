"""
修复权限问题并重启 Web 界面
==========================

修复 allow_llm_to_see_data 权限问题
"""

import subprocess
import sys
import time

def show_fix_info():
    """显示修复信息"""
    print("🔧 修复 Vanna 权限问题")
    print("=" * 40)
    
    print("❌ 问题:")
    print("   The LLM is not allowed to see the data in your database.")
    print("   Your question requires database introspection to generate the necessary SQL.")
    print("   Please set allow_llm_to_see_data=True to enable this.")
    
    print("\n✅ 解决方案:")
    print("   已在所有配置文件中添加: vn.allow_llm_to_see_data = True")
    
    print("\n📁 修改的文件:")
    print("   - vanna_deepseek_mysql.py")
    print("   - start_web_mysql.py") 
    print("   - chinese_vanna_config.py")

def restart_web_interface():
    """重启 Web 界面"""
    print("\n🚀 重启 Web 界面...")
    print("=" * 30)
    
    print("💡 使用修复后的配置重启")
    
    try:
        # 运行修复后的 Web 启动脚本
        subprocess.run([sys.executable, "start_web_mysql.py"], cwd=".")
    except KeyboardInterrupt:
        print("\n👋 Web 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def test_permission_fix():
    """测试权限修复"""
    print("🧪 测试权限修复")
    print("=" * 20)
    
    try:
        # 检查配置文件是否包含权限设置
        files_to_check = [
            "vanna_deepseek_mysql.py",
            "start_web_mysql.py",
            "chinese_vanna_config.py"
        ]
        
        for file_name in files_to_check:
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'allow_llm_to_see_data = True' in content:
                        print(f"✅ {file_name} - 权限设置已添加")
                    else:
                        print(f"❌ {file_name} - 权限设置缺失")
            except FileNotFoundError:
                print(f"⚠️ {file_name} - 文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 Vanna 权限问题修复工具")
    print("=" * 50)
    
    # 显示修复信息
    show_fix_info()
    
    # 测试修复
    if test_permission_fix():
        print("\n✅ 权限修复验证通过")
        
        # 询问是否重启
        restart = input("\n是否立即重启 Web 界面? (y/n): ").lower()
        if restart == 'y':
            restart_web_interface()
        else:
            print("\n💡 手动重启命令:")
            print("   python start_web_mysql.py")
    else:
        print("\n❌ 权限修复验证失败")

if __name__ == "__main__":
    main()
