"""
Vanna AI 简单入门示例
====================

这是一个最简单的 Vanna 使用示例，使用示例数据库。
"""

import os

def install_vanna():
    """安装 Vanna"""
    print("正在安装 Vanna...")
    os.system("pip install vanna")
    print("Vanna 安装完成！")

def simple_example():
    """最简单的 Vanna 示例"""
    try:
        import vanna
        from vanna.remote import VannaDefault
        
        print("=== Vanna AI 简单示例 ===")
        print("使用预训练的 Chinook 数据库模型")
        
        # 获取 API 密钥（第一次使用时需要提供邮箱）
        email = input("请输入你的邮箱地址: ")
        api_key = vanna.get_api_key(email)
        
        # 初始化 Vanna
        vn = VannaDefault(model='chinook', api_key=api_key)
        
        # 连接到示例数据库
        print("连接到示例数据库...")
        vn.connect_to_sqlite('https://vanna.ai/Chinook.sqlite')
        
        print("数据库连接成功！")
        print("你现在可以用自然语言查询数据库了。")
        print("示例问题:")
        print("- What are the top 10 albums by sales?")
        print("- Show me all customers from USA")
        print("- What is the total revenue by year?")
        
        # 交互式查询
        while True:
            question = input("\n请输入你的问题 (输入 'quit' 退出): ")
            if question.lower() == 'quit':
                break
                
            try:
                print("正在生成 SQL 查询...")
                result = vn.ask(question)
                print("查询完成！")
                print(f"结果: {result}")
            except Exception as e:
                print(f"查询出错: {e}")
                
    except ImportError:
        print("Vanna 未安装，正在安装...")
        install_vanna()
        print("请重新运行此脚本")
    except Exception as e:
        print(f"出现错误: {e}")

if __name__ == "__main__":
    simple_example()
