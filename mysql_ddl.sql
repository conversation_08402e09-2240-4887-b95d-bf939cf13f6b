-- MySQL DDL for database: project
-- Generated on: 2025-08-27 10:37:11.736186

-- Table: 0_sys_user_backup20220209
CREATE TABLE `0_sys_user_backup20220209` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(30) DEFAULT NULL,
  `pname` varchar(50) DEFAULT NULL,
  `password` varchar(40) DEFAULT NULL,
  `ctime` datetime DEFAULT CURRENT_TIMESTAMP,
  `type` int(11) DEFAULT NULL,
  `officeType` int(1) DEFAULT '0' COMMENT '0工程1采购2财务法务',
  `office` int(1) DEFAULT '1' COMMENT '1工程2采购3财务4法务',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=MyISAM AUTO_INCREMENT=106 DEFAULT CHARSET=utf8 COMMENT='系统用户';

-- Table: company
CREATE TABLE `company` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '分公司ID',
  `name` varchar(16) NOT NULL COMMENT '分公司名称',
  `prefix` varchar(16) NOT NULL COMMENT '缩写前缀',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COMMENT='县级分公司';

-- Table: sys_project_info
CREATE TABLE `sys_project_info` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `aid` varchar(100) DEFAULT NULL COMMENT '档案编号',
  `pid` varchar(100) DEFAULT NULL COMMENT '方案编号',
  `pname` varchar(64) DEFAULT NULL COMMENT '方案名称(或文件名)',
  `ddept` varchar(64) DEFAULT NULL COMMENT '设计单位',
  `designer` varchar(64) DEFAULT NULL COMMENT '设计人',
  `mtime` date DEFAULT NULL COMMENT '提交时间',
  `mcost` decimal(10,2) DEFAULT '0.00' COMMENT '设计材料费',
  `lcost` decimal(10,2) DEFAULT '0.00' COMMENT '设计人工费',
  `total` decimal(10,2) DEFAULT '0.00' COMMENT '总费用',
  `status` int(2) NOT NULL DEFAULT '0' COMMENT '0未验 1已验 2已审',
  `construction` varchar(64) DEFAULT NULL COMMENT '施工单位',
  `applicant` varchar(64) DEFAULT NULL COMMENT '申验人',
  `atime` date DEFAULT NULL COMMENT '报验(验收)时间',
  `scmsetup` date DEFAULT NULL COMMENT '供应链立项时间',
  `acceptance` varchar(128) DEFAULT NULL COMMENT '验收人',
  `amcost` decimal(10,2) DEFAULT '0.00' COMMENT '验收材料费',
  `alcost` decimal(10,2) DEFAULT '0.00' COMMENT '验收人工费',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `poster` varchar(64) DEFAULT NULL COMMENT '录入人',
  `ptime` datetime DEFAULT NULL COMMENT '录入时间',
  `updby` varchar(32) DEFAULT NULL COMMENT '更新人',
  `updtime` datetime DEFAULT NULL COMMENT '更新时间',
  `pfile` varchar(80) DEFAULT NULL COMMENT '设计方案',
  `acctime` date DEFAULT NULL COMMENT '验收时间',
  `aremark` varchar(1024) DEFAULT NULL COMMENT '验收备注',
  `del` int(11) DEFAULT '0' COMMENT '0默认1删除',
  `efile` varchar(80) DEFAULT NULL COMMENT '验收文件',
  `wtime` date DEFAULT NULL,
  `audit_id` varchar(100) DEFAULT '' COMMENT '结算审核报告书编号',
  `audit_department` varchar(100) DEFAULT '' COMMENT '审价机构',
  `audit_mcost` decimal(10,2) DEFAULT '0.00' COMMENT '结算施工费',
  `audit_lcost` decimal(10,2) DEFAULT '0.00' COMMENT '审定施工费',
  `audit_deduction` decimal(10,2) DEFAULT '0.00' COMMENT '审减额',
  `audit_name` varchar(100) DEFAULT '' COMMENT '审计单位审核人',
  `audit_principal` varchar(100) DEFAULT '' COMMENT '审计单位负责人',
  `construction_name` varchar(100) DEFAULT '' COMMENT '施工单位经办人',
  `construction_principal` varchar(100) DEFAULT '' COMMENT '施工单位负责人',
  `audit_file` varchar(60) DEFAULT '' COMMENT '审计报告扫描附件',
  `audit_status` int(1) NOT NULL DEFAULT '0' COMMENT '1需要审计标记',
  `audit_time` datetime DEFAULT NULL COMMENT '审计时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=11302 DEFAULT CHARSET=utf8 COMMENT='工程文档';

-- Table: sys_purchase
CREATE TABLE `sys_purchase` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT '' COMMENT '资料名称',
  `type` int(11) DEFAULT '1' COMMENT '1采购合同2招标文件3申请',
  `contractNum` varchar(200) DEFAULT '' COMMENT '合同编号/招标编号',
  `manufacturer` varchar(200) DEFAULT '' COMMENT '厂家名称',
  `date` date DEFAULT NULL,
  `amount` varchar(20) DEFAULT '' COMMENT '金额',
  `path` varchar(200) DEFAULT '' COMMENT '路径',
  `ctime` datetime DEFAULT NULL,
  `del` int(1) DEFAULT '0' COMMENT '0正常1删除',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=utf8;

-- Table: sys_user
CREATE TABLE `sys_user` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(30) DEFAULT NULL,
  `pname` varchar(50) DEFAULT NULL,
  `password` varchar(40) DEFAULT NULL,
  `ctime` datetime DEFAULT CURRENT_TIMESTAMP,
  `type` int(11) DEFAULT NULL,
  `officeType` int(1) DEFAULT '0' COMMENT '0工程1采购2财务法务',
  `office` int(1) DEFAULT '1' COMMENT '1工程2采购3财务4法务',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=MyISAM AUTO_INCREMENT=121 DEFAULT CHARSET=utf8 COMMENT='系统用户';

