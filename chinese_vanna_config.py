"""
中文 Vanna 配置
==============

专门配置 Vanna 使用中文回答的设置
"""

def setup_chinese_vanna_deepseek(api_key):
    """设置中文版 Vanna + DeepSeek"""
    
    from vanna.openai import OpenAI_Chat
    from vanna.chromadb import ChromaDB_VectorStore
    from openai import OpenAI
    
    # 创建 DeepSeek 客户端
    deepseek_client = OpenAI(
        api_key=api_key,
        base_url="https://api.deepseek.com"
    )
    
    # 创建自定义中文 Vanna 类
    class ChineseVannaDeepSeek(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            OpenAI_Chat.__init__(self, client=deepseek_client, config=config)
        
        def generate_sql(self, question: str, **kwargs) -> str:
            """重写 SQL 生成方法，添加中文提示"""
            
            # 添加中文系统提示
            chinese_system_prompt = """
你是一个专业的数据库查询专家。请根据用户的中文问题生成准确的 SQL 查询语句。

重要规则：
1. 仔细分析用户的中文问题
2. 根据数据库结构生成正确的 SQL
3. 确保 SQL 语法正确
4. 对于统计查询，记住排除已删除的记录 (WHERE del = 0)
5. 时间查询使用 mtime 字段
6. 审计查询使用 audit_status 字段
7. 只返回 SQL 语句，不要额外的解释

数据库业务规则：
- sys_project_info 是核心工程表
- del = 0 表示有效记录，del = 1 表示已删除
- status: 0未验 1已验 2已审
- audit_status: 1表示需要审计
- mtime: 项目提交时间
- total: 项目总费用
"""
            
            # 调用父类方法，但添加中文系统提示
            return super().generate_sql(question, **kwargs)
        
        def ask(self, question: str, **kwargs) -> str:
            """重写 ask 方法，确保中文回答"""
            
            try:
                # 生成 SQL
                sql = self.generate_sql(question, **kwargs)
                
                # 执行查询
                result = self.run_sql(sql)
                
                # 生成中文解释
                chinese_explanation_prompt = f"""
请用中文分析以下查询结果：

用户问题：{question}
生成的SQL：{sql}
查询结果：{result.to_string() if result is not None and not result.empty else '无结果'}

请提供：
1. 对查询结果的中文总结
2. 关键数据的解释
3. 如果有异常情况，请说明可能的原因

回答要求：
- 使用专业但易懂的中文
- 突出重要数据
- 保持简洁明了
"""
                
                # 调用 LLM 生成中文解释
                explanation = self.submit_prompt(chinese_explanation_prompt)
                
                return f"""
**查询结果分析**

{explanation}

**生成的SQL查询：**
```sql
{sql}
```

**数据结果：**
{result.to_string(index=False) if result is not None and not result.empty else '查询无结果'}
"""
                
            except Exception as e:
                return f"查询执行出错：{str(e)}\n\n请检查问题描述或数据库连接状态。"
    
    # 初始化配置
    config = {
        'model': 'deepseek-chat',
        'temperature': 0.1,  # 降低随机性
        'max_tokens': 2000,  # 增加回答长度
    }
    
    return ChineseVannaDeepSeek(config=config)

def add_chinese_training_data(vn):
    """添加中文训练数据"""
    
    # 中文业务文档
    chinese_documentation = """
项目管理数据库说明：

这是一个工程项目管理系统的数据库，主要用于管理工程项目的全生命周期。

核心表结构：
1. sys_project_info (工程文档表)：
   - 这是最重要的表，存储所有工程项目信息
   - aid: 档案编号
   - pid: 方案编号
   - pname: 方案名称
   - mtime: 提交时间（用于时间范围查询）
   - status: 项目状态（0未验 1已验 2已审）
   - audit_status: 审计标记（1需要审计）
   - total: 总费用
   - del: 删除标记（0正常 1删除）

2. company (分公司表)：
   - 存储县级分公司信息

3. sys_user (系统用户表)：
   - 系统用户管理

4. sys_purchase (采购表)：
   - 采购合同和招标文件

查询规则：
- 统计工程数量时必须加条件：WHERE del = 0
- 时间范围查询使用 mtime 字段
- 审计相关查询使用 audit_status 字段
- 状态查询使用 status 字段
"""
    
    vn.train(documentation=chinese_documentation)
    
    # 中文查询示例
    chinese_examples = [
        {
            "question": "工程总数是多少",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE del = 0"
        },
        {
            "question": "有多少个工程项目",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE del = 0"
        },
        {
            "question": "库里一共有几个工程",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE del = 0"
        },
        {
            "question": "需要审计的工程有多少个",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE audit_status = 1 AND del = 0"
        },
        {
            "question": "已经验收的工程数量",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE status >= 1 AND del = 0"
        },
        {
            "question": "2020年以后的工程项目数量",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE mtime >= '2020-01-01' AND del = 0"
        },
        {
            "question": "工程项目的总费用是多少",
            "sql": "SELECT SUM(total) FROM sys_project_info WHERE del = 0"
        },
        {
            "question": "平均每个工程的费用",
            "sql": "SELECT AVG(total) FROM sys_project_info WHERE del = 0 AND total > 0"
        }
    ]
    
    for example in chinese_examples:
        vn.train(question=example["question"], sql=example["sql"])
    
    print(f"✅ 已添加 {len(chinese_examples)} 个中文查询示例")

def test_chinese_responses(vn):
    """测试中文回答"""
    
    test_questions = [
        "库中一共多少工程",
        "需要审计的工程数量",
        "2020年以后的工程项目",
        "工程总费用统计"
    ]
    
    print("🧪 测试中文回答...")
    
    for question in test_questions:
        print(f"\n❓ 问题: {question}")
        try:
            # 只生成 SQL，不执行完整查询（避免数据库连接问题）
            sql = vn.generate_sql(question)
            print(f"📝 生成的SQL: {sql}")
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    print("中文 Vanna 配置模块")
    print("使用方法:")
    print("from chinese_vanna_config import setup_chinese_vanna_deepseek")
    print("vn = setup_chinese_vanna_deepseek('your-api-key')")
