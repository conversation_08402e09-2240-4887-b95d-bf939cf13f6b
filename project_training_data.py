"""
项目管理数据库训练数据
====================

基于实际的 project 数据库结构和业务场景的 Vanna 训练数据
"""

def train_project_database(vn):
    """使用项目管理数据库的训练数据"""
    
    print("🎓 使用项目管理数据库训练数据...")
    
    # 1. 基础统计查询
    training_data = [
        {
            "question": "库中一共多少工程",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE del = 0"
        },
        {
            "question": "工程项目总数",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE del = 0"
        },
        {
            "question": "有效的工程数量",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE del = 0"
        },
        
        # 2. 时间范围查询
        {
            "question": "2020年以后的工程有多少项",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE mtime >= '2020-01-01' AND del = 0"
        },
        {
            "question": "2021年的工程项目",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE YEAR(mtime) = 2021 AND del = 0"
        },
        {
            "question": "2022年提交的工程",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE YEAR(mtime) = 2022 AND del = 0"
        },
        {
            "question": "最近一年的工程数量",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE mtime >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR) AND del = 0"
        },
        {
            "question": "本月提交的工程",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE YEAR(mtime) = YEAR(CURDATE()) AND MONTH(mtime) = MONTH(CURDATE()) AND del = 0"
        },
        
        # 3. 审计相关查询
        {
            "question": "已经审计的工程占比",
            "sql": "SELECT ROUND(COUNT(CASE WHEN audit_status = 1 THEN 1 END) * 100.0 / COUNT(*), 2) AS audit_percentage FROM sys_project_info WHERE del = 0"
        },
        {
            "question": "需要审计的工程数量",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE audit_status = 1 AND del = 0"
        },
        {
            "question": "已完成审计的工程",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE audit_time IS NOT NULL AND del = 0"
        },
        {
            "question": "未审计的工程数量",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE audit_status = 0 AND del = 0"
        },
        
        # 4. 状态相关查询
        {
            "question": "已验收的工程数量",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE status >= 1 AND del = 0"
        },
        {
            "question": "未验收的工程",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE status = 0 AND del = 0"
        },
        {
            "question": "已审核的工程",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE status = 2 AND del = 0"
        },
        {
            "question": "各个状态的工程分布",
            "sql": "SELECT CASE WHEN status = 0 THEN '未验' WHEN status = 1 THEN '已验' WHEN status = 2 THEN '已审' END AS status_name, COUNT(*) as count FROM sys_project_info WHERE del = 0 GROUP BY status"
        },
        
        # 5. 费用相关查询
        {
            "question": "总费用超过10万的工程",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE total > 100000 AND del = 0"
        },
        {
            "question": "工程总费用统计",
            "sql": "SELECT SUM(total) as total_amount, AVG(total) as avg_amount FROM sys_project_info WHERE del = 0"
        },
        {
            "question": "平均工程费用",
            "sql": "SELECT AVG(total) FROM sys_project_info WHERE del = 0 AND total > 0"
        },
        {
            "question": "费用最高的10个工程",
            "sql": "SELECT pname, total FROM sys_project_info WHERE del = 0 ORDER BY total DESC LIMIT 10"
        },
        {
            "question": "费用在5万到10万之间的工程",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE total BETWEEN 50000 AND 100000 AND del = 0"
        },
        
        # 6. 设计单位相关查询
        {
            "question": "有多少个设计单位",
            "sql": "SELECT COUNT(DISTINCT ddept) FROM sys_project_info WHERE del = 0 AND ddept IS NOT NULL"
        },
        {
            "question": "各设计单位的工程数量",
            "sql": "SELECT ddept, COUNT(*) as project_count FROM sys_project_info WHERE del = 0 AND ddept IS NOT NULL GROUP BY ddept ORDER BY project_count DESC"
        },
        {
            "question": "工程最多的设计单位",
            "sql": "SELECT ddept, COUNT(*) as project_count FROM sys_project_info WHERE del = 0 AND ddept IS NOT NULL GROUP BY ddept ORDER BY project_count DESC LIMIT 1"
        },
        
        # 7. 施工单位相关查询
        {
            "question": "有多少个施工单位",
            "sql": "SELECT COUNT(DISTINCT construction) FROM sys_project_info WHERE del = 0 AND construction IS NOT NULL"
        },
        {
            "question": "各施工单位的工程数量",
            "sql": "SELECT construction, COUNT(*) as project_count FROM sys_project_info WHERE del = 0 AND construction IS NOT NULL GROUP BY construction ORDER BY project_count DESC"
        },
        
        # 8. 复合查询
        {
            "question": "2020年以后已审计的工程数量",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE mtime >= '2020-01-01' AND audit_status = 1 AND del = 0"
        },
        {
            "question": "2021年费用超过5万的工程",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE YEAR(mtime) = 2021 AND total > 50000 AND del = 0"
        },
        {
            "question": "已验收但未审计的工程",
            "sql": "SELECT COUNT(*) FROM sys_project_info WHERE status >= 1 AND audit_status = 0 AND del = 0"
        },
        
        # 9. 按年度统计
        {
            "question": "按年度统计工程数量",
            "sql": "SELECT YEAR(mtime) as year, COUNT(*) as count FROM sys_project_info WHERE del = 0 AND mtime IS NOT NULL GROUP BY YEAR(mtime) ORDER BY year DESC"
        },
        {
            "question": "按年度统计工程总费用",
            "sql": "SELECT YEAR(mtime) as year, SUM(total) as total_amount FROM sys_project_info WHERE del = 0 AND mtime IS NOT NULL GROUP BY YEAR(mtime) ORDER BY year DESC"
        },
        
        # 10. 用户和采购相关
        {
            "question": "系统用户总数",
            "sql": "SELECT COUNT(*) FROM sys_user"
        },
        {
            "question": "工程部门用户数量",
            "sql": "SELECT COUNT(*) FROM sys_user WHERE office = 1"
        },
        {
            "question": "采购合同数量",
            "sql": "SELECT COUNT(*) FROM sys_purchase WHERE type = 1 AND del = 0"
        },
        {
            "question": "招标文件数量",
            "sql": "SELECT COUNT(*) FROM sys_purchase WHERE type = 2 AND del = 0"
        },
        {
            "question": "分公司数量",
            "sql": "SELECT COUNT(*) FROM company"
        }
    ]
    
    # 执行训练
    for i, data in enumerate(training_data, 1):
        print(f"   {i:2d}. 训练: {data['question']}")
        vn.train(question=data['question'], sql=data['sql'])
    
    print(f"✅ 完成 {len(training_data)} 个查询示例的训练")

def train_business_documentation(vn):
    """训练业务文档"""
    
    documentation = """
    项目管理数据库业务说明：
    
    核心业务流程：
    1. 工程项目提交 (mtime 提交时间)
    2. 项目验收 (status: 0→1→2)
    3. 审计流程 (audit_status = 1 需要审计)
    4. 费用结算 (total 总费用)
    
    重要字段说明：
    - del = 0: 有效记录，del = 1: 已删除
    - status: 0未验 1已验 2已审
    - audit_status: 0不需要审计 1需要审计
    - mtime: 项目提交时间，用于时间范围查询
    - total: 项目总费用
    - aid: 档案编号，pid: 方案编号
    
    查询注意事项：
    - 统计工程数量时必须加 WHERE del = 0
    - 时间查询使用 mtime 字段
    - 审计查询使用 audit_status 字段
    - 费用查询使用 total 字段
    """
    
    vn.train(documentation=documentation)
    print("✅ 业务文档训练完成")

if __name__ == "__main__":
    print("项目管理数据库训练数据")
    print("请在 Vanna 脚本中调用:")
    print("from project_training_data import train_project_database")
    print("train_project_database(vn)")
