# Vanna AI 核心包
vanna>=0.7.9

# LLM 提供商
openai>=1.0.0  # 用于 DeepSeek (必需)
# anthropic  # 如果使用 Claude
# google-generativeai  # 如果使用 Gemini

# 可选的向量数据库
# chromadb  # 推荐用于开始
# qdrant-client  # 如果使用 Qdrant
# pinecone-client  # 如果使用 Pinecone

# 数据库驱动
pymysql>=1.0.0  # MySQL (必需)
# psycopg2-binary  # PostgreSQL
# snowflake-connector-python  # Snowflake
# google-cloud-bigquery  # BigQuery

# Web 界面相关
# streamlit  # 如果使用 Streamlit 界面
# chainlit  # 如果使用 Chainlit 界面

# 开发工具
jupyter  # 用于 Jupyter Notebook 开发
