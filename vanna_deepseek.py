"""
Vanna + DeepSeek 配置
====================

使用 DeepSeek API 作为 LLM 提供商的 Vanna 配置
"""

import os
import socket

def check_deepseek_requirements():
    """检查 DeepSeek 所需的依赖"""
    try:
        import openai
        print("✅ OpenAI 客户端库已安装")
        return True
    except ImportError:
        print("📦 需要安装 OpenAI 客户端库...")
        import subprocess
        import sys
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'openai'])
            print("✅ OpenAI 客户端库安装成功")
            return True
        except Exception as e:
            print(f"❌ 安装失败: {e}")
            return False

def setup_vanna_deepseek():
    """设置 Vanna + DeepSeek"""
    
    # 检查依赖
    if not check_deepseek_requirements():
        return None
    
    from vanna.openai import OpenAI_Chat
    from vanna.chromadb import ChromaDB_VectorStore
    from openai import OpenAI
    
    # 获取 DeepSeek API 密钥
    api_key = input("请输入你的 DeepSeek API 密钥: ").strip()
    if not api_key:
        print("❌ 需要 DeepSeek API 密钥")
        print("💡 获取方式: https://platform.deepseek.com/")
        return None
    
    # 创建 DeepSeek 客户端
    deepseek_client = OpenAI(
        api_key=api_key,
        base_url="https://api.deepseek.com"
    )
    
    # 创建自定义 Vanna 类
    class VannaDeepSeek(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            # 使用 DeepSeek 客户端初始化 OpenAI_Chat
            OpenAI_Chat.__init__(self, client=deepseek_client, config=config)
    
    # 初始化 Vanna
    vn = VannaDeepSeek(config={'model': 'deepseek-chat'})
    
    print("✅ Vanna + DeepSeek 初始化成功！")
    return vn

def create_sample_database():
    """创建示例数据库"""
    import sqlite3
    
    print("📊 创建示例数据库...")
    
    conn = sqlite3.connect('deepseek_demo.db')
    cursor = conn.cursor()
    
    # 创建员工表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS employees (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            department TEXT NOT NULL,
            salary REAL NOT NULL,
            hire_date TEXT NOT NULL,
            manager_id INTEGER,
            FOREIGN KEY (manager_id) REFERENCES employees(id)
        )
    """)
    
    # 创建项目表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS projects (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            department TEXT NOT NULL,
            budget REAL NOT NULL,
            start_date TEXT NOT NULL,
            end_date TEXT,
            status TEXT DEFAULT 'active'
        )
    """)
    
    # 创建员工项目关联表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS employee_projects (
            employee_id INTEGER,
            project_id INTEGER,
            role TEXT,
            hours_allocated REAL,
            PRIMARY KEY (employee_id, project_id),
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (project_id) REFERENCES projects(id)
        )
    """)
    
    # 插入示例数据
    employees_data = [
        (1, '张三', 'IT', 12000, '2023-01-15', None),
        (2, '李四', 'IT', 10000, '2023-02-20', 1),
        (3, '王五', '销售', 8000, '2023-03-10', None),
        (4, '赵六', '销售', 7000, '2023-04-05', 3),
        (5, '钱七', '市场', 9000, '2023-05-12', None),
        (6, '孙八', '人力资源', 8500, '2023-06-18', None),
    ]
    
    projects_data = [
        (1, '网站重构', 'IT', 50000, '2023-01-01', '2023-06-30', 'completed'),
        (2, '移动应用开发', 'IT', 80000, '2023-03-01', '2023-12-31', 'active'),
        (3, '市场推广活动', '市场', 30000, '2023-02-01', '2023-08-31', 'active'),
        (4, '销售培训项目', '销售', 20000, '2023-04-01', '2023-10-31', 'active'),
    ]
    
    employee_projects_data = [
        (1, 1, '项目经理', 40),
        (2, 1, '开发工程师', 35),
        (1, 2, '技术顾问', 20),
        (2, 2, '主开发', 40),
        (5, 3, '项目经理', 30),
        (3, 4, '培训师', 25),
        (4, 4, '助理', 15),
    ]
    
    cursor.executemany("INSERT OR REPLACE INTO employees VALUES (?, ?, ?, ?, ?, ?)", employees_data)
    cursor.executemany("INSERT OR REPLACE INTO projects VALUES (?, ?, ?, ?, ?, ?, ?)", projects_data)
    cursor.executemany("INSERT OR REPLACE INTO employee_projects VALUES (?, ?, ?, ?)", employee_projects_data)
    
    conn.commit()
    conn.close()
    
    print("✅ 示例数据库创建完成: deepseek_demo.db")

def train_vanna_model(vn):
    """训练 Vanna 模型"""
    print("🎓 训练 Vanna 模型...")
    
    # 训练 DDL
    vn.train(ddl="""
        CREATE TABLE employees (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            department TEXT NOT NULL,
            salary REAL NOT NULL,
            hire_date TEXT NOT NULL,
            manager_id INTEGER,
            FOREIGN KEY (manager_id) REFERENCES employees(id)
        );
        
        CREATE TABLE projects (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            department TEXT NOT NULL,
            budget REAL NOT NULL,
            start_date TEXT NOT NULL,
            end_date TEXT,
            status TEXT DEFAULT 'active'
        );
        
        CREATE TABLE employee_projects (
            employee_id INTEGER,
            project_id INTEGER,
            role TEXT,
            hours_allocated REAL,
            PRIMARY KEY (employee_id, project_id),
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (project_id) REFERENCES projects(id)
        );
    """)
    
    # 训练文档
    vn.train(documentation="""
        这是一个公司管理数据库：
        - employees 表存储员工信息，包括姓名、部门、薪资等
        - projects 表存储项目信息，包括预算、时间等
        - employee_projects 表存储员工参与项目的关联信息
        - 部门包括：IT、销售、市场、人力资源
        - 项目状态包括：active（进行中）、completed（已完成）、cancelled（已取消）
    """)
    
    # 训练示例 SQL
    example_sqls = [
        "SELECT department, COUNT(*) as employee_count FROM employees GROUP BY department",
        "SELECT name, salary FROM employees WHERE department = 'IT' ORDER BY salary DESC",
        "SELECT p.name, p.budget, COUNT(ep.employee_id) as team_size FROM projects p LEFT JOIN employee_projects ep ON p.id = ep.project_id GROUP BY p.id",
    ]
    
    for sql in example_sqls:
        vn.train(sql=sql)
    
    print("✅ 模型训练完成！")

def demo_queries(vn):
    """演示查询"""
    print("\n🤖 开始演示查询...")
    
    questions = [
        "每个部门有多少员工？",
        "IT部门薪资最高的员工是谁？",
        "正在进行的项目有哪些？",
        "哪个项目的团队规模最大？",
        "平均薪资是多少？"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n❓ 问题 {i}: {question}")
        
        try:
            # 生成 SQL
            sql = vn.generate_sql(question)
            print(f"📝 生成的 SQL:")
            print(sql)
            
            # 执行查询
            result = vn.run_sql(sql)
            print(f"📊 结果:")
            if result is not None and not result.empty:
                print(result.to_string(index=False))
            else:
                print("无结果")
                
        except Exception as e:
            print(f"❌ 查询失败: {e}")

def start_web_interface(vn):
    """启动 Web 界面"""
    try:
        from vanna.flask import VannaFlaskApp
        
        # 找到可用端口
        def find_available_port(start_port=5000):
            for port in range(start_port, start_port + 10):
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.bind(('127.0.0.1', port))
                        return port
                except OSError:
                    continue
            return None
        
        port = find_available_port()
        if port is None:
            print("❌ 无法找到可用端口")
            return
        
        print(f"\n🌐 启动 Web 界面在端口 {port}")
        print(f"📱 访问地址: http://localhost:{port}")
        print("⏹️ 按 Ctrl+C 停止服务器")
        
        # 自动打开浏览器
        import webbrowser
        import threading
        import time
        
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{port}')
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        # 启动应用
        app = VannaFlaskApp(vn)
        app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)
        
    except Exception as e:
        print(f"❌ Web 界面启动失败: {e}")

def main():
    """主函数"""
    print("🚀 Vanna + DeepSeek 演示")
    print("=" * 40)
    
    # 设置 Vanna + DeepSeek
    vn = setup_vanna_deepseek()
    if vn is None:
        return
    
    # 创建示例数据库
    create_sample_database()
    
    # 连接数据库
    vn.connect_to_sqlite('deepseek_demo.db')
    print("✅ 数据库连接成功")
    
    # 训练模型
    train_vanna_model(vn)
    
    print("\n🎯 选择使用方式:")
    print("1. 命令行演示查询")
    print("2. 启动 Web 界面")
    print("3. 交互式查询")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        demo_queries(vn)
    elif choice == "2":
        start_web_interface(vn)
    elif choice == "3":
        print("\n🤖 交互式查询模式")
        print("输入 'quit' 退出")
        
        while True:
            question = input("\n❓ 你的问题: ").strip()
            if question.lower() == 'quit':
                break
            if not question:
                continue
                
            try:
                print("🔄 正在生成 SQL...")
                sql = vn.generate_sql(question)
                print(f"📝 SQL: {sql}")
                
                result = vn.run_sql(sql)
                print("📊 结果:")
                if result is not None and not result.empty:
                    print(result.to_string(index=False))
                else:
                    print("无结果")
            except Exception as e:
                print(f"❌ 错误: {e}")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
