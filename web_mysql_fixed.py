"""
修复权限问题的 MySQL Web 界面
============================

预配置版本，修复 allow_llm_to_see_data 问题
"""

import socket
import webbrowser
import threading
import time

def find_available_port(start_port=5000):
    """找到可用端口"""
    for port in range(start_port, start_port + 20):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return port
        except OSError:
            continue
    return None

def setup_vanna_with_permissions():
    """设置带权限的 Vanna"""
    
    from vanna.openai import OpenAI_Chat
    from vanna.chromadb import ChromaDB_VectorStore
    from openai import OpenAI
    
    # 使用你之前输入的 API 密钥（需要你手动替换）
    DEEPSEEK_API_KEY = "sk-your-deepseek-api-key-here"  # 👈 请替换为你的实际 API 密钥
    
    if DEEPSEEK_API_KEY == "sk-your-deepseek-api-key-here":
        print("❌ 请在代码中设置你的 DeepSeek API 密钥")
        print("💡 修改第 28 行的 DEEPSEEK_API_KEY 变量")
        return None
    
    # 创建 DeepSeek 客户端
    deepseek_client = OpenAI(
        api_key=DEEPSEEK_API_KEY,
        base_url="https://api.deepseek.com"
    )
    
    # 创建自定义 Vanna 类
    class VannaDeepSeekFixed(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            OpenAI_Chat.__init__(self, client=deepseek_client, config=config)
    
    # 初始化 Vanna
    vn = VannaDeepSeekFixed(config={
        'model': 'deepseek-chat',
        'temperature': 0.1,
        'max_tokens': 2000,
    })
    
    # 🔧 关键修复：设置允许 LLM 查看数据
    vn.allow_llm_to_see_data = True
    
    print("✅ Vanna + DeepSeek 初始化成功！(权限已修复)")
    return vn

def connect_to_project_mysql(vn):
    """连接到项目 MySQL 数据库"""
    
    # 使用之前测试成功的连接信息
    MYSQL_CONFIG = {
        'host': '***********',
        'port': 3306,
        'user': 'root',
        'password': 'your-password-here',  # 👈 请替换为你的实际密码
        'database': 'project'
    }
    
    if MYSQL_CONFIG['password'] == 'your-password-here':
        print("❌ 请在代码中设置你的 MySQL 密码")
        print("💡 修改第 60 行的 password 字段")
        return False
    
    try:
        print("🔄 连接到项目数据库...")
        
        vn.connect_to_mysql(
            host=MYSQL_CONFIG['host'],
            dbname=MYSQL_CONFIG['database'],
            user=MYSQL_CONFIG['user'],
            password=MYSQL_CONFIG['password'],
            port=MYSQL_CONFIG['port']
        )
        
        print("✅ MySQL 连接成功！")
        print(f"📊 已连接到: {MYSQL_CONFIG['user']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}")
        return True
        
    except Exception as e:
        print(f"❌ MySQL 连接失败: {e}")
        return False

def quick_train(vn):
    """快速训练"""
    print("🎓 快速训练数据库...")
    
    try:
        # 获取表信息并训练
        tables_result = vn.run_sql("SHOW TABLES")
        if tables_result is not None and not tables_result.empty:
            table_names = tables_result.iloc[:, 0].tolist()
            print(f"📊 找到 {len(table_names)} 个表: {', '.join(table_names)}")
            
            # 训练核心表结构
            for table_name in ['sys_project_info', 'company', 'sys_user', 'sys_purchase']:
                if table_name in table_names:
                    try:
                        create_result = vn.run_sql(f"SHOW CREATE TABLE `{table_name}`")
                        if create_result is not None and not create_result.empty:
                            create_sql = create_result.iloc[0, 1]
                            vn.train(ddl=create_sql)
                            print(f"   ✅ {table_name}")
                    except Exception as e:
                        print(f"   ❌ {table_name}: {e}")
        
        # 添加核心业务文档
        vn.train(documentation="""
            项目管理数据库：
            - sys_project_info: 工程项目核心表
            - del = 0: 有效记录，del = 1: 已删除
            - status: 0未验 1已验 2已审
            - audit_status: 1需要审计
            - mtime: 提交时间
            - total: 总费用
        """)
        
        # 添加核心查询示例
        core_examples = [
            ("库中一共多少工程", "SELECT COUNT(*) FROM sys_project_info WHERE del = 0"),
            ("需要审计的工程数量", "SELECT COUNT(*) FROM sys_project_info WHERE audit_status = 1 AND del = 0"),
            ("2020年以后的工程", "SELECT COUNT(*) FROM sys_project_info WHERE mtime >= '2020-01-01' AND del = 0"),
        ]
        
        for question, sql in core_examples:
            vn.train(question=question, sql=sql)
        
        print("✅ 快速训练完成！")
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return False

def start_web_interface(vn):
    """启动 Web 界面"""
    try:
        from vanna.flask import VannaFlaskApp
        
        print("🌐 启动 Web 界面...")
        
        # 找到可用端口
        port = find_available_port()
        if port is None:
            print("❌ 无法找到可用端口")
            return
        
        print(f"🚀 Web 服务器启动在端口 {port}")
        print(f"📱 访问地址: http://localhost:{port}")
        
        # 自动打开浏览器
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🌐 浏览器已自动打开")
            except Exception as e:
                print(f"⚠️ 请手动访问: http://localhost:{port}")
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        print("\n🎯 权限问题已修复！")
        print("✅ allow_llm_to_see_data = True")
        print("\n💡 现在可以正常查询了:")
        print("   - 库中一共多少工程")
        print("   - 需要审计的工程数量")
        print("   - 2020年以后的工程项目")
        
        print("\n⏹️ 按 Ctrl+C 停止服务器")
        
        # 启动应用
        app = VannaFlaskApp(vn)
        app.run(
            host='127.0.0.1',
            port=port,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Web 服务器已停止")
    except Exception as e:
        print(f"❌ Web 界面启动失败: {e}")

def main():
    """主函数"""
    print("🔧 修复权限问题并启动 Web 界面")
    print("=" * 50)
    
    # 显示修复信息
    show_fix_info()
    
    print("\n⚠️ 使用前请确保:")
    print("1. 在第 28 行设置你的 DeepSeek API 密钥")
    print("2. 在第 60 行设置你的 MySQL 密码")
    
    proceed = input("\n是否已设置密钥和密码? (y/n): ").lower()
    if proceed != 'y':
        print("💡 请先编辑文件设置密钥和密码，然后重新运行")
        return
    
    # 设置 Vanna
    vn = setup_vanna_with_permissions()
    if vn is None:
        return
    
    # 连接数据库
    if not connect_to_project_mysql(vn):
        return
    
    # 快速训练
    if not quick_train(vn):
        print("⚠️ 训练失败，但仍可继续使用")
    
    # 启动 Web 界面
    start_web_interface(vn)

if __name__ == "__main__":
    main()
