"""
Vanna + DeepSeek + MySQL 专用版本
===============================

专门用于连接 MySQL 数据库的 Vanna + DeepSeek 配置
"""

import os
import socket

def check_mysql_requirements():
    """检查 MySQL 所需的依赖"""
    try:
        import pymysql
        print("✅ PyMySQL 已安装")
        return True
    except ImportError:
        print("📦 需要安装 PyMySQL...")
        import subprocess
        import sys
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pymysql'])
            print("✅ PyMySQL 安装成功")
            return True
        except Exception as e:
            print(f"❌ 安装失败: {e}")
            return False

def setup_vanna_deepseek():
    """设置 Vanna + DeepSeek"""
    
    # 检查依赖
    try:
        import openai
        print("✅ OpenAI 客户端库已安装")
    except ImportError:
        print("📦 需要安装 OpenAI 客户端库...")
        import subprocess
        import sys
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'openai'])
            print("✅ OpenAI 客户端库安装成功")
        except Exception as e:
            print(f"❌ 安装失败: {e}")
            return None
    
    from vanna.openai import OpenAI_Chat
    from vanna.chromadb import ChromaDB_VectorStore
    from openai import OpenAI
    
    # 获取 DeepSeek API 密钥
    api_key = input("请输入你的 DeepSeek API 密钥: ").strip()
    if not api_key:
        print("❌ 需要 DeepSeek API 密钥")
        print("💡 获取方式: https://platform.deepseek.com/")
        return None
    
    # 创建 DeepSeek 客户端
    deepseek_client = OpenAI(
        api_key=api_key,
        base_url="https://api.deepseek.com"
    )
    
    # 创建自定义 Vanna 类
    class VannaDeepSeek(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            ChromaDB_VectorStore.__init__(self, config=config)
            # 使用 DeepSeek 客户端初始化 OpenAI_Chat
            OpenAI_Chat.__init__(self, client=deepseek_client, config=config)
    
    # 初始化 Vanna
    vn = VannaDeepSeek(config={'model': 'deepseek-chat'})
    
    print("✅ Vanna + DeepSeek 初始化成功！")
    return vn

def connect_to_mysql(vn):
    """连接到 MySQL 数据库"""
    
    if not check_mysql_requirements():
        return False
    
    print("🔗 MySQL 数据库连接配置")
    print("=" * 30)
    
    # 获取连接信息
    host = input("主机地址 (默认: localhost): ").strip() or "localhost"
    port = input("端口 (默认: 3306): ").strip() or "3306"
    user = input("用户名: ").strip()
    password = input("密码: ").strip()
    database = input("数据库名: ").strip()
    
    if not all([user, password, database]):
        print("❌ 用户名、密码和数据库名不能为空")
        return False
    
    try:
        print("🔄 正在连接到 MySQL...")
        
        # 连接到 MySQL
        vn.connect_to_mysql(
            host=host,
            dbname=database,
            user=user,
            password=password,
            port=int(port)
        )
        
        print("✅ MySQL 数据库连接成功！")
        print(f"📊 已连接到: {user}@{host}:{port}/{database}")
        return True
        
    except Exception as e:
        print(f"❌ MySQL 连接失败: {e}")
        print("\n💡 常见问题解决:")
        print("1. 检查 MySQL 服务是否启动")
        print("2. 验证用户名和密码")
        print("3. 确认数据库名称正确")
        print("4. 检查防火墙设置")
        print("5. 确认用户有访问权限")
        return False

def get_mysql_schema_info(vn):
    """获取 MySQL 数据库结构信息"""
    try:
        print("📋 获取数据库结构信息...")
        
        # 获取所有表名
        tables_result = vn.run_sql("SHOW TABLES")
        
        if tables_result is not None and not tables_result.empty:
            table_names = tables_result.iloc[:, 0].tolist()
            print(f"📊 找到 {len(table_names)} 个表:")
            for table in table_names:
                print(f"   - {table}")
            return table_names
        else:
            print("⚠️ 没有找到表")
            return []
            
    except Exception as e:
        print(f"❌ 获取表信息失败: {e}")
        return []

def auto_train_from_mysql(vn, table_names):
    """从 MySQL 自动生成训练数据"""
    
    if not table_names:
        print("⚠️ 没有表可以训练")
        return
    
    print("🎓 自动从 MySQL 生成训练数据...")
    
    try:
        # 为每个表生成 DDL
        for table_name in table_names:
            print(f"📋 处理表: {table_name}")
            
            try:
                # 获取建表语句
                create_result = vn.run_sql(f"SHOW CREATE TABLE `{table_name}`")
                
                if create_result is not None and not create_result.empty:
                    create_sql = create_result.iloc[0, 1]  # 第二列是建表语句
                    
                    # 训练 DDL
                    vn.train(ddl=create_sql)
                    print(f"   ✅ DDL 训练完成")
                    
            except Exception as e:
                print(f"   ❌ 处理表 {table_name} 失败: {e}")
        
        # 添加通用的 MySQL 文档
        vn.train(documentation="""
            这是一个 MySQL 数据库。
            - 使用标准的 SQL 语法
            - 支持 JOIN、GROUP BY、ORDER BY 等操作
            - 字符串使用单引号或双引号
            - 日期格式通常为 'YYYY-MM-DD'
        """)
        
        print("✅ 自动训练完成！")
        
    except Exception as e:
        print(f"❌ 自动训练失败: {e}")

def interactive_query(vn):
    """交互式查询"""
    print("\n🤖 交互式查询模式")
    print("=" * 30)
    print("💡 提示:")
    print("- 可以用中文或英文提问")
    print("- 输入 'quit' 退出")
    print("- 输入 'tables' 查看所有表")
    
    while True:
        question = input("\n❓ 你的问题: ").strip()
        
        if question.lower() == 'quit':
            print("👋 再见！")
            break
        elif question.lower() == 'tables':
            try:
                tables = vn.run_sql("SHOW TABLES")
                print("📊 数据库中的表:")
                for i, table in enumerate(tables.iloc[:, 0], 1):
                    print(f"   {i}. {table}")
            except Exception as e:
                print(f"❌ 获取表列表失败: {e}")
            continue
        elif not question:
            continue
        
        try:
            print("🔄 正在生成 SQL...")
            
            # 生成 SQL
            sql = vn.generate_sql(question)
            print(f"\n📝 生成的 SQL:")
            print(sql)
            
            # 执行查询
            print("\n⚡ 执行查询...")
            result = vn.run_sql(sql)
            
            if result is not None and not result.empty:
                print(f"\n📊 查询结果 ({len(result)} 行):")
                # 限制显示行数
                if len(result) > 10:
                    print(result.head(10).to_string(index=False))
                    print(f"... (还有 {len(result) - 10} 行)")
                else:
                    print(result.to_string(index=False))
            else:
                print("📭 查询无结果")
                
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            print("💡 提示: 尝试重新表述你的问题")

def main():
    """主函数"""
    print("🚀 Vanna + DeepSeek + MySQL")
    print("=" * 40)
    
    # 设置 Vanna + DeepSeek
    vn = setup_vanna_deepseek()
    if vn is None:
        return
    
    # 连接到 MySQL
    if not connect_to_mysql(vn):
        return
    
    # 获取数据库结构
    table_names = get_mysql_schema_info(vn)
    
    # 询问是否自动训练
    if table_names:
        auto_train = input("\n是否自动从数据库结构生成训练数据? (y/n): ").lower()
        if auto_train == 'y':
            auto_train_from_mysql(vn, table_names)
    
    # 开始交互式查询
    interactive_query(vn)

if __name__ == "__main__":
    main()
