
# 使用生成的 DDL 训练 Vanna
# 文件: mysql_ddl.sql

# 方法 1: 直接使用整个 DDL 文件
with open('mysql_ddl.sql', 'r', encoding='utf-8') as f:
    ddl_content = f.read()
    vn.train(ddl=ddl_content)

# 方法 2: 按表分别训练（推荐）
ddl_statements = []
current_statement = ""

with open('mysql_ddl.sql', 'r', encoding='utf-8') as f:
    for line in f:
        if line.strip() and not line.startswith('--'):
            current_statement += line
            if line.strip().endswith(';'):
                ddl_statements.append(current_statement.strip())
                current_statement = ""

for ddl in ddl_statements:
    if ddl.upper().startswith('CREATE TABLE'):
        vn.train(ddl=ddl)
        print(f"✅ 训练完成: {ddl[:50]}...")
