"""
快速启动 Web 界面
================

使用之前成功的配置快速启动 Web 界面
"""

import socket
import webbrowser
import threading
import time

def find_available_port(start_port=5000):
    """找到可用端口"""
    for port in range(start_port, start_port + 20):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return port
        except OSError:
            continue
    return None

def main():
    """主函数"""
    print("🚀 快速启动 Web 界面")
    print("=" * 40)
    
    # 获取必要信息
    print("请提供以下信息:")
    deepseek_key = input("DeepSeek API 密钥: ").strip()
    mysql_password = input("MySQL 密码: ").strip()
    
    if not deepseek_key or not mysql_password:
        print("❌ 需要提供 API 密钥和密码")
        return
    
    try:
        from vanna.openai import OpenAI_Chat
        from vanna.chromadb import ChromaDB_VectorStore
        from vanna.flask import VannaFlaskApp
        from openai import OpenAI
        
        print("🔧 初始化 Vanna + DeepSeek...")
        
        # 创建 DeepSeek 客户端
        deepseek_client = OpenAI(
            api_key=deepseek_key,
            base_url="https://api.deepseek.com"
        )
        
        # 创建 Vanna 类
        class VannaDeepSeek(ChromaDB_VectorStore, OpenAI_Chat):
            def __init__(self, config=None):
                ChromaDB_VectorStore.__init__(self, config=config)
                OpenAI_Chat.__init__(self, client=deepseek_client, config=config)
        
        # 初始化
        vn = VannaDeepSeek(config={
            'model': 'deepseek-chat',
            'temperature': 0.1,
            'max_tokens': 2000,
        })
        
        # 🔧 关键修复：设置允许 LLM 查看数据
        vn.allow_llm_to_see_data = True
        
        print("✅ Vanna 初始化成功！")
        
        # 连接 MySQL
        print("🔗 连接到 MySQL...")
        vn.connect_to_mysql(
            host='***********',
            dbname='project',
            user='root',
            password=mysql_password,
            port=3306
        )
        print("✅ MySQL 连接成功！")
        
        # 快速训练
        print("🎓 快速训练...")
        
        # 添加核心业务文档
        vn.train(documentation="""
            项目管理数据库：
            - sys_project_info: 工程项目核心表
            - del = 0: 有效记录，del = 1: 已删除
            - status: 0未验 1已验 2已审
            - audit_status: 1需要审计
            - mtime: 提交时间
            - total: 总费用
            
            查询规则：
            - 统计工程时必须加 WHERE del = 0
            - 时间查询使用 mtime 字段
            - 审计查询使用 audit_status 字段
        """)
        
        # 添加核心查询示例
        examples = [
            ("库中一共多少工程", "SELECT COUNT(*) FROM sys_project_info WHERE del = 0"),
            ("需要审计的工程数量", "SELECT COUNT(*) FROM sys_project_info WHERE audit_status = 1 AND del = 0"),
            ("2020年以后的工程", "SELECT COUNT(*) FROM sys_project_info WHERE mtime >= '2020-01-01' AND del = 0"),
            ("已验收的工程数量", "SELECT COUNT(*) FROM sys_project_info WHERE status >= 1 AND del = 0"),
        ]
        
        for question, sql in examples:
            vn.train(question=question, sql=sql)
        
        print("✅ 训练完成！")
        
        # 启动 Web 界面
        port = find_available_port()
        if port is None:
            print("❌ 无法找到可用端口")
            return
        
        print(f"🌐 启动 Web 界面在端口 {port}")
        print(f"📱 访问地址: http://localhost:{port}")
        
        # 自动打开浏览器
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open(f'http://localhost:{port}')
                print("🌐 浏览器已自动打开")
            except Exception:
                print(f"请手动访问: http://localhost:{port}")
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        print("\n🎯 权限问题已修复！")
        print("✅ allow_llm_to_see_data = True")
        print("\n💡 现在可以正常查询:")
        print("   - 库中一共多少工程")
        print("   - 需要审计的工程数量") 
        print("   - 2020年以后的工程项目")
        
        print("\n⏹️ 按 Ctrl+C 停止服务器")
        
        # 启动 Flask 应用
        app = VannaFlaskApp(vn)
        app.run(
            host='127.0.0.1',
            port=port,
            debug=False,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Web 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请检查:")
        print("1. DeepSeek API 密钥是否正确")
        print("2. MySQL 密码是否正确")
        print("3. 网络连接是否正常")

if __name__ == "__main__":
    main()
