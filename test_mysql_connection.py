"""
测试 MySQL 连接
==============

快速测试 MySQL 数据库连接是否正常
"""

def test_mysql_connection():
    """测试 MySQL 连接"""
    
    try:
        import pymysql
        print("✅ PyMySQL 已安装")
    except ImportError:
        print("❌ 需要安装 pymysql: pip install pymysql")
        return False
    
    print("🧪 MySQL 连接测试")
    print("=" * 30)
    
    # 获取连接信息
    host = input("主机地址: ").strip()
    port = input("端口 (默认: 3306): ").strip() or "3306"
    user = input("用户名: ").strip()
    password = input("密码: ").strip()
    
    if not all([host, user, password]):
        print("❌ 主机地址、用户名和密码不能为空")
        return False
    
    try:
        print(f"🔄 正在连接到 {user}@{host}:{port}...")
        
        # 测试连接（不指定数据库）
        connection = pymysql.connect(
            host=host,
            user=user,
            password=password,
            port=int(port),
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 测试基本查询
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"✅ 连接成功！MySQL 版本: {version}")
        
        # 获取数据库列表
        cursor.execute("SHOW DATABASES")
        databases = [db[0] for db in cursor.fetchall()]
        
        print(f"\n📊 可用的数据库 ({len(databases)} 个):")
        for i, db in enumerate(databases, 1):
            print(f"   {i}. {db}")
        
        # 让用户选择数据库
        if databases:
            print(f"\n请选择要使用的数据库:")
            choice = input("输入数据库名称: ").strip()
            
            if choice in databases:
                # 测试选择的数据库
                cursor.execute(f"USE `{choice}`")
                cursor.execute("SHOW TABLES")
                tables = [table[0] for table in cursor.fetchall()]
                
                print(f"\n📋 数据库 '{choice}' 中的表 ({len(tables)} 个):")
                for i, table in enumerate(tables, 1):
                    print(f"   {i}. {table}")
                
                connection.close()
                
                print(f"\n🎯 连接信息确认:")
                print(f"   主机: {host}:{port}")
                print(f"   用户: {user}")
                print(f"   数据库: {choice}")
                print(f"   表数量: {len(tables)}")
                
                return {
                    'host': host,
                    'port': int(port),
                    'user': user,
                    'password': password,
                    'database': choice,
                    'tables': tables
                }
            else:
                print(f"❌ 数据库 '{choice}' 不存在")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("\n💡 常见问题:")
        print("1. 检查 MySQL 服务是否启动")
        print("2. 验证主机地址和端口")
        print("3. 确认用户名和密码")
        print("4. 检查防火墙设置")
        print("5. 确认用户有远程连接权限")
        return False

def create_connection_config(conn_info):
    """创建连接配置文件"""
    
    if not isinstance(conn_info, dict):
        return
    
    config_content = f'''"""
MySQL 连接配置
=============

根据测试结果生成的连接配置
"""

# MySQL 连接信息
MYSQL_CONFIG = {{
    'host': '{conn_info['host']}',
    'port': {conn_info['port']},
    'user': '{conn_info['user']}',
    'password': '{conn_info['password']}',  # 注意：实际使用时请妥善保管密码
    'database': '{conn_info['database']}'
}}

# 在 Vanna 中使用
def connect_vanna_to_mysql(vn):
    """连接 Vanna 到 MySQL"""
    vn.connect_to_mysql(
        host=MYSQL_CONFIG['host'],
        dbname=MYSQL_CONFIG['database'],
        user=MYSQL_CONFIG['user'],
        password=MYSQL_CONFIG['password'],
        port=MYSQL_CONFIG['port']
    )
    print("✅ Vanna MySQL 连接成功！")

# 数据库表信息
TABLES = {conn_info['tables']}

print(f"数据库 '{{MYSQL_CONFIG['database']}}' 包含 {{len(TABLES)}} 个表:")
for table in TABLES:
    print(f"  - {{table}}")
'''
    
    with open('mysql_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ 连接配置已保存到: mysql_config.py")

def main():
    """主函数"""
    print("🔗 MySQL 连接测试工具")
    print("=" * 40)
    
    conn_info = test_mysql_connection()
    
    if isinstance(conn_info, dict):
        print("\n🎉 连接测试成功！")
        
        save_config = input("\n是否保存连接配置? (y/n): ").lower()
        if save_config == 'y':
            create_connection_config(conn_info)
        
        print("\n🚀 下一步:")
        print("1. 运行: python vanna_deepseek_mysql.py")
        print("2. 使用刚才测试的连接信息")
        print("3. 开始用自然语言查询数据库！")
    
    elif conn_info:
        print("\n✅ 基本连接成功，但需要选择数据库")
        print("请重新运行并选择一个数据库")
    else:
        print("\n❌ 连接失败，请检查连接信息")

if __name__ == "__main__":
    main()
