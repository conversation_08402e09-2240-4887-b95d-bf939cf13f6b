"""
修复端口访问问题
===============

诊断和修复 Vanna Web 应用的端口访问问题
"""

import socket
import subprocess
import sys
import os

def check_port_status(port):
    """检查端口状态"""
    try:
        # 尝试绑定端口
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('127.0.0.1', port))
            return "可用"
    except OSError as e:
        if e.errno == 10048:  # Windows: 地址已在使用中
            return "被占用"
        elif e.errno == 10013:  # Windows: 权限被拒绝
            return "权限被拒绝"
        else:
            return f"错误: {e}"

def find_process_using_port(port):
    """查找占用端口的进程"""
    try:
        result = subprocess.run(
            ['netstat', '-ano'], 
            capture_output=True, 
            text=True, 
            shell=True
        )
        
        lines = result.stdout.split('\n')
        for line in lines:
            if f':{port}' in line and 'LISTENING' in line:
                parts = line.split()
                if len(parts) >= 5:
                    pid = parts[-1]
                    return pid
        return None
    except Exception as e:
        print(f"查找进程时出错: {e}")
        return None

def get_process_name(pid):
    """根据 PID 获取进程名"""
    try:
        result = subprocess.run(
            ['tasklist', '/FI', f'PID eq {pid}', '/FO', 'CSV'],
            capture_output=True,
            text=True,
            shell=True
        )
        
        lines = result.stdout.strip().split('\n')
        if len(lines) > 1:
            # 解析 CSV 格式的输出
            process_info = lines[1].split(',')
            if len(process_info) > 0:
                return process_info[0].strip('"')
        return "未知进程"
    except Exception:
        return "未知进程"

def diagnose_port_issues():
    """诊断端口问题"""
    print("🔍 端口问题诊断")
    print("=" * 30)
    
    # 检查常用端口
    ports = [5000, 5001, 5002, 8000, 8080, 3000]
    
    available_ports = []
    
    for port in ports:
        status = check_port_status(port)
        print(f"端口 {port}: {status}")
        
        if status == "可用":
            available_ports.append(port)
        elif status == "被占用":
            pid = find_process_using_port(port)
            if pid:
                process_name = get_process_name(pid)
                print(f"  └─ 被进程占用: {process_name} (PID: {pid})")
    
    print(f"\n✅ 可用端口: {available_ports}")
    
    if available_ports:
        recommended_port = available_ports[0]
        print(f"💡 推荐使用端口: {recommended_port}")
        return recommended_port
    else:
        print("❌ 没有找到可用端口")
        return None

def check_firewall_settings():
    """检查防火墙设置"""
    print("\n🛡️ 防火墙检查")
    print("=" * 20)
    
    print("请检查以下设置:")
    print("1. Windows 防火墙是否阻止了 Python 应用")
    print("2. 杀毒软件是否阻止了网络连接")
    print("3. 公司网络是否有端口限制")
    
    print("\n💡 解决方案:")
    print("1. 将 Python 添加到防火墙例外")
    print("2. 临时关闭防火墙测试")
    print("3. 使用不同的端口")

def create_simple_test_server(port):
    """创建简单的测试服务器"""
    print(f"\n🧪 创建测试服务器在端口 {port}")
    
    test_server_code = f'''
import http.server
import socketserver
import webbrowser
import threading
import time

PORT = {port}

class MyHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = f"""
            <html>
            <head><title>Vanna 端口测试</title></head>
            <body>
                <h1>🎉 端口 {PORT} 工作正常！</h1>
                <p>如果你能看到这个页面，说明端口访问没有问题。</p>
                <p>现在可以尝试启动 Vanna Web 应用了。</p>
            </body>
            </html>
            """
            self.wfile.write(html.encode())
        else:
            super().do_GET()

def open_browser():
    time.sleep(1)
    webbrowser.open(f'http://localhost:{PORT}')

if __name__ == "__main__":
    try:
        with socketserver.TCPServer(("", PORT), MyHandler) as httpd:
            print(f"测试服务器启动在端口 {PORT}")
            print(f"请访问: http://localhost:{PORT}")
            
            # 自动打开浏览器
            threading.Thread(target=open_browser, daemon=True).start()
            
            print("按 Ctrl+C 停止服务器")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {{e}}")
'''
    
    # 保存测试服务器代码
    with open(f'test_server_{port}.py', 'w', encoding='utf-8') as f:
        f.write(test_server_code)
    
    print(f"✅ 测试服务器代码已保存到 test_server_{port}.py")
    print(f"运行命令: python test_server_{port}.py")

def main():
    """主函数"""
    print("🔧 Vanna 端口问题修复工具")
    print("=" * 40)
    
    # 诊断端口问题
    recommended_port = diagnose_port_issues()
    
    # 检查防火墙
    check_firewall_settings()
    
    if recommended_port:
        print(f"\n🚀 建议的解决方案:")
        print(f"1. 使用端口 {recommended_port} 启动 Vanna")
        print(f"2. 创建测试服务器验证端口")
        
        choice = input(f"\n是否创建端口 {recommended_port} 的测试服务器? (y/n): ").lower()
        if choice == 'y':
            create_simple_test_server(recommended_port)
            
            run_test = input("是否立即运行测试服务器? (y/n): ").lower()
            if run_test == 'y':
                os.system(f'python test_server_{recommended_port}.py')
    
    print("\n📝 总结:")
    print("1. 如果测试服务器能正常访问，说明端口没问题")
    print("2. 如果仍然无法访问，请检查防火墙设置")
    print("3. 可以尝试使用其他可用端口")
    print("4. 确保以管理员权限运行 Python 脚本")

if __name__ == "__main__":
    main()
