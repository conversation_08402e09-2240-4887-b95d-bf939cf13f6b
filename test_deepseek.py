"""
测试 DeepSeek 配置
=================

验证 DeepSeek + Vanna 配置是否正确
"""

def test_deepseek_connection():
    """测试 DeepSeek 连接"""
    try:
        from openai import OpenAI
        
        print("🧪 测试 DeepSeek 连接")
        print("=" * 30)
        
        # 获取 API 密钥
        api_key = input("请输入你的 DeepSeek API 密钥 (测试用): ").strip()
        if not api_key:
            print("⏭️ 跳过连接测试")
            return False
        
        # 创建客户端
        client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )
        
        print("🔄 测试 API 连接...")
        
        # 简单的测试请求
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "user", "content": "Hello, this is a test. Please respond with 'DeepSeek API is working!'"}
            ],
            max_tokens=50
        )
        
        result = response.choices[0].message.content
        print(f"✅ DeepSeek API 响应: {result}")
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek 连接测试失败: {e}")
        return False

def test_vanna_deepseek_integration():
    """测试 Vanna + DeepSeek 集成"""
    try:
        from vanna.openai import OpenAI_Chat
        from vanna.chromadb import ChromaDB_VectorStore
        from openai import OpenAI
        
        print("\n🔧 测试 Vanna + DeepSeek 集成")
        print("=" * 40)
        
        # 获取 API 密钥
        api_key = input("请输入你的 DeepSeek API 密钥: ").strip()
        if not api_key:
            print("⏭️ 跳过集成测试")
            return False
        
        # 创建 DeepSeek 客户端
        deepseek_client = OpenAI(
            api_key=api_key,
            base_url="https://api.deepseek.com"
        )
        
        # 创建 Vanna 类
        class VannaDeepSeek(ChromaDB_VectorStore, OpenAI_Chat):
            def __init__(self, config=None):
                ChromaDB_VectorStore.__init__(self, config=config)
                OpenAI_Chat.__init__(self, client=deepseek_client, config=config)
        
        # 初始化
        print("🔄 初始化 Vanna + DeepSeek...")
        vn = VannaDeepSeek(config={'model': 'deepseek-chat'})
        
        # 简单的 SQL 生成测试
        print("🔄 测试 SQL 生成...")
        
        # 添加简单的训练数据
        vn.train(ddl="""
            CREATE TABLE users (
                id INTEGER PRIMARY KEY,
                name TEXT,
                age INTEGER
            );
        """)
        
        # 测试 SQL 生成
        test_question = "Show all users"
        sql = vn.generate_sql(test_question)
        
        print(f"✅ SQL 生成测试成功!")
        print(f"   问题: {test_question}")
        print(f"   生成的 SQL: {sql}")
        
        return True
        
    except Exception as e:
        print(f"❌ Vanna + DeepSeek 集成测试失败: {e}")
        return False

def show_deepseek_info():
    """显示 DeepSeek 信息"""
    print("📚 DeepSeek 信息")
    print("=" * 20)
    
    print("🔗 获取 API 密钥:")
    print("   https://platform.deepseek.com/")
    
    print("\n💰 定价信息:")
    print("   - 新用户有免费额度")
    print("   - 比 OpenAI 便宜很多")
    print("   - 按 token 使用量计费")
    
    print("\n🤖 可用模型:")
    print("   - deepseek-chat: 通用对话模型")
    print("   - deepseek-coder: 代码生成专用模型 (推荐用于 SQL)")
    
    print("\n🌟 优势:")
    print("   - 中文支持优秀")
    print("   - 代码生成能力强")
    print("   - 成本效益高")

def main():
    """主函数"""
    print("🧪 DeepSeek + Vanna 测试工具")
    print("=" * 40)
    
    print("选择测试项目:")
    print("1. DeepSeek API 连接测试")
    print("2. Vanna + DeepSeek 集成测试")
    print("3. 查看 DeepSeek 信息")
    print("4. 全部测试")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        test_deepseek_connection()
    elif choice == "2":
        test_vanna_deepseek_integration()
    elif choice == "3":
        show_deepseek_info()
    elif choice == "4":
        show_deepseek_info()
        if test_deepseek_connection():
            test_vanna_deepseek_integration()
    else:
        print("❌ 无效选择")
    
    print("\n🎯 下一步:")
    print("1. 如果测试通过，运行: python vanna_deepseek.py")
    print("2. 或者创建 Notebook: python create_deepseek_notebook.py")
    print("3. 查看详细指南: deepseek_setup_guide.md")

if __name__ == "__main__":
    main()
